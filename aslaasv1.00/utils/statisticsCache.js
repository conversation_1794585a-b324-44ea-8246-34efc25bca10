const NodeCache = require('node-cache');

class StatisticsCache {
  constructor() {
    // Create cache instances with different TTL for different types of data
    this.commandStatsCache = new NodeCache({ 
      stdTTL: 300, // 5 minutes for command statistics
      checkperiod: 60 // Check for expired keys every minute
    });
    
    this.userStatsCache = new NodeCache({ 
      stdTTL: 600, // 10 minutes for user statistics
      checkperiod: 120
    });
    
    this.deviceStatsCache = new NodeCache({ 
      stdTTL: 300, // 5 minutes for device statistics
      checkperiod: 60
    });
    
    this.timeBasedCache = new NodeCache({ 
      stdTTL: 900, // 15 minutes for time-based statistics
      checkperiod: 180
    });
    
    // Cache for aggregated statistics (longer TTL)
    this.aggregatedCache = new NodeCache({ 
      stdTTL: 1800, // 30 minutes for aggregated data
      checkperiod: 300
    });
  }

  // Generate cache key from filters
  generateCacheKey(prefix, filters = {}) {
    const sortedFilters = Object.keys(filters)
      .sort()
      .reduce((result, key) => {
        if (filters[key] !== undefined && filters[key] !== null && filters[key] !== '') {
          result[key] = filters[key];
        }
        return result;
      }, {});
    
    return `${prefix}_${JSON.stringify(sortedFilters)}`;
  }

  // Command statistics cache methods
  getCommandStats(filters) {
    const key = this.generateCacheKey('command_stats', filters);
    return this.commandStatsCache.get(key);
  }

  setCommandStats(filters, data) {
    const key = this.generateCacheKey('command_stats', filters);
    return this.commandStatsCache.set(key, data);
  }

  invalidateCommandStats(filters = null) {
    if (filters) {
      const key = this.generateCacheKey('command_stats', filters);
      this.commandStatsCache.del(key);
    } else {
      this.commandStatsCache.flushAll();
    }
  }

  // User statistics cache methods
  getUserStats(filters) {
    const key = this.generateCacheKey('user_stats', filters);
    return this.userStatsCache.get(key);
  }

  setUserStats(filters, data) {
    const key = this.generateCacheKey('user_stats', filters);
    return this.userStatsCache.set(key, data);
  }

  invalidateUserStats(filters = null) {
    if (filters) {
      const key = this.generateCacheKey('user_stats', filters);
      this.userStatsCache.del(key);
    } else {
      this.userStatsCache.flushAll();
    }
  }

  // Device statistics cache methods
  getDeviceStats(filters) {
    const key = this.generateCacheKey('device_stats', filters);
    return this.deviceStatsCache.get(key);
  }

  setDeviceStats(filters, data) {
    const key = this.generateCacheKey('device_stats', filters);
    return this.deviceStatsCache.set(key, data);
  }

  invalidateDeviceStats(filters = null) {
    if (filters) {
      const key = this.generateCacheKey('device_stats', filters);
      this.deviceStatsCache.del(key);
    } else {
      this.deviceStatsCache.flushAll();
    }
  }

  // Time-based statistics cache methods
  getTimeBasedStats(filters) {
    const key = this.generateCacheKey('time_stats', filters);
    return this.timeBasedCache.get(key);
  }

  setTimeBasedStats(filters, data) {
    const key = this.generateCacheKey('time_stats', filters);
    return this.timeBasedCache.set(key, data);
  }

  invalidateTimeBasedStats(filters = null) {
    if (filters) {
      const key = this.generateCacheKey('time_stats', filters);
      this.timeBasedCache.del(key);
    } else {
      this.timeBasedCache.flushAll();
    }
  }

  // Aggregated statistics cache methods
  getAggregatedStats(type, period) {
    const key = `aggregated_${type}_${period}`;
    return this.aggregatedCache.get(key);
  }

  setAggregatedStats(type, period, data) {
    const key = `aggregated_${type}_${period}`;
    return this.aggregatedCache.set(key, data);
  }

  invalidateAggregatedStats(type = null, period = null) {
    if (type && period) {
      const key = `aggregated_${type}_${period}`;
      this.aggregatedCache.del(key);
    } else {
      this.aggregatedCache.flushAll();
    }
  }

  // Invalidate all caches
  invalidateAll() {
    this.commandStatsCache.flushAll();
    this.userStatsCache.flushAll();
    this.deviceStatsCache.flushAll();
    this.timeBasedCache.flushAll();
    this.aggregatedCache.flushAll();
  }

  // Get cache statistics
  getCacheStats() {
    return {
      commandStats: {
        keys: this.commandStatsCache.keys().length,
        hits: this.commandStatsCache.getStats().hits,
        misses: this.commandStatsCache.getStats().misses
      },
      userStats: {
        keys: this.userStatsCache.keys().length,
        hits: this.userStatsCache.getStats().hits,
        misses: this.userStatsCache.getStats().misses
      },
      deviceStats: {
        keys: this.deviceStatsCache.keys().length,
        hits: this.deviceStatsCache.getStats().hits,
        misses: this.deviceStatsCache.getStats().misses
      },
      timeBasedStats: {
        keys: this.timeBasedCache.keys().length,
        hits: this.timeBasedCache.getStats().hits,
        misses: this.timeBasedCache.getStats().misses
      },
      aggregatedStats: {
        keys: this.aggregatedCache.keys().length,
        hits: this.aggregatedCache.getStats().hits,
        misses: this.aggregatedCache.getStats().misses
      }
    };
  }

  // Cache warming - preload common queries
  async warmCache(LogModel) {
    try {
      console.log('Statistics Cache: Warming cache...');
      
      // Common time periods
      const now = new Date();
      const timeRanges = [
        { 
          startDate: new Date(now.getTime() - 24 * 60 * 60 * 1000), // Last 24 hours
          endDate: now,
          period: 'daily'
        },
        { 
          startDate: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
          endDate: now,
          period: 'daily'
        },
        { 
          startDate: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          endDate: now,
          period: 'weekly'
        }
      ];

      // Preload basic statistics for common time ranges
      for (const range of timeRanges) {
        // Basic command stats
        const commandCount = await LogModel.countDocuments({
          createdAt: { $gte: range.startDate, $lte: range.endDate }
        });
        
        this.setAggregatedStats('commands', range.period, { count: commandCount });
        
        // Basic success rate
        const successCount = await LogModel.countDocuments({
          createdAt: { $gte: range.startDate, $lte: range.endDate },
          success: true
        });
        
        this.setAggregatedStats('success_rate', range.period, { 
          total: commandCount, 
          success: successCount,
          rate: commandCount > 0 ? (successCount / commandCount) * 100 : 0
        });
      }
      
      console.log('Statistics Cache: Cache warming completed');
    } catch (error) {
      console.error('Statistics Cache: Error warming cache', error);
    }
  }

  // Scheduled cache invalidation for real-time updates
  scheduleInvalidation() {
    // Invalidate command stats every 2 minutes to keep them fresh
    setInterval(() => {
      this.invalidateCommandStats();
    }, 120000);

    // Invalidate device stats every 3 minutes
    setInterval(() => {
      this.invalidateDeviceStats();
    }, 180000);

    // Invalidate user stats every 5 minutes
    setInterval(() => {
      this.invalidateUserStats();
    }, 300000);
  }
}

// Create singleton instance
const statisticsCache = new StatisticsCache();

module.exports = statisticsCache;
