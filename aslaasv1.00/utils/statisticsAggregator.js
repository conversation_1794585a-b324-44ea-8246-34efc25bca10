const cron = require('node-cron');
const LogModel = require('../models/log');
const CommandStatistics = require('../models/commandStatistics');
const UserActivityStatistics = require('../models/userActivityStatistics');
const DeviceStatistics = require('../models/deviceStatistics');
const statisticsCache = require('./statisticsCache');

class StatisticsAggregator {
  constructor() {
    this.isRunning = false;
    this.jobs = new Map();
  }

  // Start all scheduled jobs
  start() {
    if (this.isRunning) {
      console.log('Statistics Aggregator: Already running');
      return;
    }

    console.log('Statistics Aggregator: Starting scheduled jobs');
    this.isRunning = true;

    // Daily aggregation at 2 AM
    this.jobs.set('daily', cron.schedule('0 2 * * *', () => {
      this.aggregateDailyStatistics();
    }, { scheduled: false }));

    // Hourly aggregation
    this.jobs.set('hourly', cron.schedule('0 * * * *', () => {
      this.aggregateHourlyStatistics();
    }, { scheduled: false }));

    // Cache warming every 30 minutes
    this.jobs.set('cache-warm', cron.schedule('*/30 * * * *', () => {
      this.warmCache();
    }, { scheduled: false }));

    // Cache cleanup every 6 hours
    this.jobs.set('cache-cleanup', cron.schedule('0 */6 * * *', () => {
      this.cleanupCache();
    }, { scheduled: false }));

    // Start all jobs
    this.jobs.forEach((job, name) => {
      job.start();
      console.log(`Statistics Aggregator: Started ${name} job`);
    });

    // Initial cache warming
    this.warmCache();
  }

  // Stop all scheduled jobs
  stop() {
    if (!this.isRunning) {
      return;
    }

    console.log('Statistics Aggregator: Stopping scheduled jobs');
    this.jobs.forEach((job, name) => {
      job.stop();
      console.log(`Statistics Aggregator: Stopped ${name} job`);
    });

    this.isRunning = false;
  }

  // Aggregate daily statistics
  async aggregateDailyStatistics() {
    try {
      console.log('Statistics Aggregator: Starting daily aggregation');
      const startTime = Date.now();

      const today = new Date();
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
      
      // Set to start and end of day
      const startOfDay = new Date(yesterday.setHours(0, 0, 0, 0));
      const endOfDay = new Date(yesterday.setHours(23, 59, 59, 999));

      await Promise.all([
        this.aggregateCommandStatistics('daily', startOfDay, endOfDay),
        this.aggregateUserStatistics('daily', startOfDay, endOfDay),
        this.aggregateDeviceStatistics('daily', startOfDay, endOfDay)
      ]);

      const duration = Date.now() - startTime;
      console.log(`Statistics Aggregator: Daily aggregation completed in ${duration}ms`);
    } catch (error) {
      console.error('Statistics Aggregator: Error in daily aggregation', error);
    }
  }

  // Aggregate hourly statistics (for real-time insights)
  async aggregateHourlyStatistics() {
    try {
      console.log('Statistics Aggregator: Starting hourly aggregation');
      const startTime = Date.now();

      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

      // Invalidate relevant caches
      statisticsCache.invalidateCommandStats();
      statisticsCache.invalidateDeviceStats();
      statisticsCache.invalidateUserStats();

      const duration = Date.now() - startTime;
      console.log(`Statistics Aggregator: Hourly aggregation completed in ${duration}ms`);
    } catch (error) {
      console.error('Statistics Aggregator: Error in hourly aggregation', error);
    }
  }

  // Aggregate command statistics
  async aggregateCommandStatistics(period, startDate, endDate) {
    try {
      const matchConditions = {
        createdAt: { $gte: startDate, $lte: endDate }
      };

      const stats = await LogModel.aggregate([
        { $match: matchConditions },
        {
          $group: {
            _id: null,
            totalCommands: { $sum: 1 },
            successCount: { $sum: { $cond: [{ $eq: ["$success", true] }, 1, 0] } },
            failureCount: { $sum: { $cond: [{ $eq: ["$success", false] }, 1, 0] } },
            avgResponseTime: { $avg: "$responseTime" },
            minResponseTime: { $min: "$responseTime" },
            maxResponseTime: { $max: "$responseTime" },
            commandTypes: {
              $push: {
                type: "$commandType",
                success: "$success"
              }
            }
          }
        }
      ]);

      if (stats.length > 0) {
        const stat = stats[0];
        
        // Count command types
        const commandTypeCounts = {};
        const failureReasons = {};
        
        stat.commandTypes.forEach(cmd => {
          const type = cmd.type || 'other';
          commandTypeCounts[type] = (commandTypeCounts[type] || 0) + 1;
        });

        // Create or update command statistics record
        await CommandStatistics.findOneAndUpdate(
          { period, date: startDate },
          {
            totalCommands: stat.totalCommands,
            successCount: stat.successCount,
            failureCount: stat.failureCount,
            commandTypes: commandTypeCounts,
            responseTimes: {
              min: stat.minResponseTime,
              max: stat.maxResponseTime,
              avg: stat.avgResponseTime
            },
            updatedAt: new Date()
          },
          { upsert: true, new: true }
        );
      }
    } catch (error) {
      console.error('Statistics Aggregator: Error aggregating command statistics', error);
    }
  }

  // Aggregate user statistics
  async aggregateUserStatistics(period, startDate, endDate) {
    try {
      const userStats = await LogModel.aggregate([
        { $match: { createdAt: { $gte: startDate, $lte: endDate } } },
        {
          $group: {
            _id: "$userId",
            phoneNumber: { $first: "$user" },
            totalCommands: { $sum: 1 },
            successfulCommands: { $sum: { $cond: [{ $eq: ["$success", true] }, 1, 0] } },
            failedCommands: { $sum: { $cond: [{ $eq: ["$success", false] }, 1, 0] } },
            avgResponseTime: { $avg: "$responseTime" },
            commandTypes: { $push: "$commandType" },
            lastActivity: { $max: "$createdAt" }
          }
        }
      ]);

      // Save user statistics
      for (const userStat of userStats) {
        if (userStat._id) {
          const commandTypeCounts = {};
          userStat.commandTypes.forEach(type => {
            const cmdType = type || 'other';
            commandTypeCounts[cmdType] = (commandTypeCounts[cmdType] || 0) + 1;
          });

          await UserActivityStatistics.findOneAndUpdate(
            { userId: userStat._id, period, date: startDate },
            {
              phoneNumber: userStat.phoneNumber,
              commandStats: {
                totalCommands: userStat.totalCommands,
                successfulCommands: userStat.successfulCommands,
                failedCommands: userStat.failedCommands,
                successRate: userStat.totalCommands > 0 
                  ? (userStat.successfulCommands / userStat.totalCommands) * 100 
                  : 0
              },
              commandTypes: commandTypeCounts,
              responseTimes: {
                avgResponseTime: userStat.avgResponseTime
              },
              lastActivity: userStat.lastActivity,
              updatedAt: new Date()
            },
            { upsert: true, new: true }
          );
        }
      }
    } catch (error) {
      console.error('Statistics Aggregator: Error aggregating user statistics', error);
    }
  }

  // Aggregate device statistics
  async aggregateDeviceStatistics(period, startDate, endDate) {
    try {
      const deviceStats = await LogModel.aggregate([
        { $match: { createdAt: { $gte: startDate, $lte: endDate } } },
        {
          $group: {
            _id: "$deviceNumber",
            deviceType: { $first: "$deviceType" },
            totalCommands: { $sum: 1 },
            successfulCommands: { $sum: { $cond: [{ $eq: ["$success", true] }, 1, 0] } },
            failedCommands: { $sum: { $cond: [{ $eq: ["$success", false] }, 1, 0] } },
            avgResponseTime: { $avg: "$responseTime" },
            minResponseTime: { $min: "$responseTime" },
            maxResponseTime: { $max: "$responseTime" },
            avgSignalStrength: { $avg: "$signalStrength" },
            lastActivity: { $max: "$createdAt" },
            commandTypes: { $push: "$commandType" }
          }
        }
      ]);

      // Save device statistics
      for (const deviceStat of deviceStats) {
        const commandTypeCounts = {};
        deviceStat.commandTypes.forEach(type => {
          const cmdType = type || 'other';
          commandTypeCounts[cmdType] = (commandTypeCounts[cmdType] || 0) + 1;
        });

        await DeviceStatistics.findOneAndUpdate(
          { deviceNumber: deviceStat._id, period, date: startDate },
          {
            deviceType: deviceStat.deviceType,
            commandStats: {
              totalCommands: deviceStat.totalCommands,
              successfulCommands: deviceStat.successfulCommands,
              failedCommands: deviceStat.failedCommands,
              successRate: deviceStat.totalCommands > 0 
                ? (deviceStat.successfulCommands / deviceStat.totalCommands) * 100 
                : 0
            },
            responsePerformance: {
              avgResponseTime: deviceStat.avgResponseTime,
              minResponseTime: deviceStat.minResponseTime,
              maxResponseTime: deviceStat.maxResponseTime
            },
            signalStats: {
              avgSignalStrength: deviceStat.avgSignalStrength
            },
            commandTypes: commandTypeCounts,
            updatedAt: new Date()
          },
          { upsert: true, new: true }
        );
      }
    } catch (error) {
      console.error('Statistics Aggregator: Error aggregating device statistics', error);
    }
  }

  // Warm cache with common queries
  async warmCache() {
    try {
      console.log('Statistics Aggregator: Warming cache');
      await statisticsCache.warmCache(LogModel);
    } catch (error) {
      console.error('Statistics Aggregator: Error warming cache', error);
    }
  }

  // Cleanup old cache entries
  cleanupCache() {
    try {
      console.log('Statistics Aggregator: Cleaning up cache');
      // The cache automatically handles TTL, but we can force cleanup of old entries
      const stats = statisticsCache.getCacheStats();
      console.log('Statistics Aggregator: Cache stats', stats);
    } catch (error) {
      console.error('Statistics Aggregator: Error cleaning up cache', error);
    }
  }
}

// Create singleton instance
const statisticsAggregator = new StatisticsAggregator();

module.exports = statisticsAggregator;
