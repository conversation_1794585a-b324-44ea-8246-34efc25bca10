const mqtt = require('mqtt');
const LogModel = require('../models/log');
const UserModel = require('../models/user');
const DeviceModel = require('../models/device');

class StatisticsMqttPublisher {
  constructor() {
    this.client = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    
    // Statistics topics
    this.topics = {
      commands: 'statistics/commands',
      devices: 'statistics/devices',
      users: 'statistics/users',
      realtime: 'statistics/realtime',
      filters: 'statistics/filters',
      refresh: 'statistics/refresh'
    };
    
    // Cache for real-time statistics
    this.realtimeCache = {
      newCommands: 0,
      activeDevices: 0,
      activeUsers: 0,
      lastReset: Date.now()
    };

    // Track commands sent to devices
    this.commandTracking = new Map();
    this.activeDevices = new Set();
    
    // Initialize connection - DISABLED
    // this.connect();

    // Set up periodic real-time updates - DISABLED
    // this.setupPeriodicUpdates();
  }

  // Connect to MQTT broker
  connect() {
    try {
      // Use the same MQTT broker as the main system
      const brokerUrl = process.env.MQTT_BROKER_URL || 'mqtt://39.104.209.84:1883';
      
      this.client = mqtt.connect(brokerUrl, {
        clientId: `statistics_publisher_${Math.random().toString(16).substring(2, 10)}`,
        clean: true,
        keepalive: 60,
        reconnectPeriod: this.reconnectDelay
      });

      this.setupEventHandlers();
    } catch (error) {
      console.error('Statistics MQTT: Connection error', error);
      this.scheduleReconnect();
    }
  }

  // Setup MQTT event handlers
  setupEventHandlers() {
    if (!this.client) return;

    this.client.on('connect', () => {
      console.log('Statistics MQTT: Publisher connected');
      this.isConnected = true;
      this.reconnectAttempts = 0;

      // Subscribe to filter and refresh requests
      this.client.subscribe([this.topics.filters, this.topics.refresh]);

      // Subscribe to all device response topics for statistics tracking
      this.client.subscribe('+/msg'); // Subscribe to all device response topics

      // Subscribe to device command topics to track commands sent
      this.subscribeToDeviceCommands();
    });

    this.client.on('disconnect', () => {
      console.log('Statistics MQTT: Publisher disconnected');
      this.isConnected = false;
    });

    this.client.on('error', (error) => {
      console.error('Statistics MQTT: Publisher error', error);
      this.isConnected = false;
      this.scheduleReconnect();
    });

    this.client.on('message', (topic, message) => {
      try {
        // Handle device responses (deviceNumber/msg pattern)
        if (topic.includes('/msg')) {
          this.handleDeviceResponse(topic, message);
        }
        // Handle device commands (deviceNumber pattern without /msg)
        else if (!topic.startsWith('statistics/') && !topic.includes('/')) {
          this.handleDeviceCommand(topic, message);
        }
        // Handle statistics control messages
        else if (topic.startsWith('statistics/')) {
          const data = JSON.parse(message.toString());

          if (topic === this.topics.filters) {
            console.log('Statistics MQTT: Received filter update', data);
            // Handle filter updates from clients
          } else if (topic === this.topics.refresh) {
            console.log('Statistics MQTT: Received refresh request', data);
            // Handle refresh requests from clients
            this.handleRefreshRequest(data);
          }
        }
      } catch (error) {
        console.error('Statistics MQTT: Error processing message', error);
      }
    });
  }

  // Schedule reconnection
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('Statistics MQTT: Max reconnect attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    console.log(`Statistics MQTT: Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts})`);
    
    setTimeout(() => {
      this.connect();
    }, delay);
  }

  // Subscribe to device command topics
  subscribeToDeviceCommands() {
    // Subscribe to common device number patterns
    // You may need to adjust this based on your device numbering scheme
    const devicePatterns = [
      '+', // All single-level topics (device numbers)
      'device/+', // If devices use device/ prefix
      'car/+', // If cars use car/ prefix
    ];

    devicePatterns.forEach(pattern => {
      this.client.subscribe(pattern);
      console.log(`Statistics MQTT: Subscribed to device commands ${pattern}`);
    });
  }

  // Handle device command messages
  handleDeviceCommand(topic, message) {
    try {
      const deviceNumber = topic;
      const command = message.toString();

      console.log(`Statistics MQTT: Command sent to device ${deviceNumber}: ${command}`);

      // Track the command
      const commandKey = `${deviceNumber}_${Date.now()}`;
      const commandData = {
        deviceNumber,
        command,
        commandType: this.determineCommandType(command),
        sentTime: Date.now(),
        userId: this.extractUserIdFromCommand(command) // Extract if available in command
      };

      this.commandTracking.set(commandKey, commandData);
      this.activeDevices.add(deviceNumber);

      // Set timeout for command response
      setTimeout(() => {
        if (this.commandTracking.has(commandKey)) {
          const timeoutData = this.commandTracking.get(commandKey);
          this.handleCommandTimeout(timeoutData);
          this.commandTracking.delete(commandKey);
        }
      }, 30000); // 30 second timeout

      // Update real-time cache
      this.realtimeCache.newCommands++;
      this.realtimeCache.activeDevices = this.activeDevices.size;

      // Publish command statistics
      this.publishCommandUpdate(commandData);

    } catch (error) {
      console.error('Statistics MQTT: Error handling device command', error);
    }
  }

  // Handle device response messages
  handleDeviceResponse(topic, message) {
    try {
      const deviceNumber = topic.replace('/msg', '');
      const response = message.toString();

      console.log(`Statistics MQTT: Response from device ${deviceNumber}: ${response}`);

      // Find matching command
      const commandKey = this.findCommandForDevice(deviceNumber);
      if (commandKey) {
        const commandData = this.commandTracking.get(commandKey);
        const responseTime = Date.now() - commandData.sentTime;

        // Create response data
        const responseData = {
          ...commandData,
          response,
          responseTime,
          receiveTime: Date.now(),
          success: this.isSuccessResponse(response),
          responseStatus: this.getResponseStatus(response)
        };

        // Publish response statistics
        this.publishCommandUpdate(responseData);

        // Remove from tracking
        this.commandTracking.delete(commandKey);
      }

    } catch (error) {
      console.error('Statistics MQTT: Error handling device response', error);
    }
  }

  // Find command for device
  findCommandForDevice(deviceNumber) {
    for (const [key, command] of this.commandTracking.entries()) {
      if (command.deviceNumber === deviceNumber) {
        return key;
      }
    }
    return null;
  }

  // Handle command timeout
  handleCommandTimeout(commandData) {
    const timeoutData = {
      ...commandData,
      responseTime: Date.now() - commandData.sentTime,
      success: false,
      responseStatus: 'timeout',
      failureReason: 'Device did not respond within timeout period'
    };

    console.log(`Statistics MQTT: Command timeout for device ${commandData.deviceNumber}`);
    this.publishCommandUpdate(timeoutData);
  }

  // Determine command type from command string
  determineCommandType(command) {
    const cmd = command.toLowerCase();

    if (cmd.includes('power') && cmd.includes('on')) return 'power_on';
    if (cmd.includes('power') && cmd.includes('off')) return 'power_off';
    if (cmd.includes('lock')) return 'lock';
    if (cmd.includes('unlock')) return 'unlock';
    if (cmd.includes('location') || cmd.includes('gps')) return 'location';
    if (cmd.includes('status') || cmd.includes('info')) return 'status';
    if (cmd.includes('config') || cmd.includes('set')) return 'config';

    return 'other';
  }

  // Extract user ID from command if available
  extractUserIdFromCommand(command) {
    // This depends on your command format
    // You might need to modify this based on how user info is included in commands
    try {
      const match = command.match(/user:(\w+)/);
      return match ? match[1] : null;
    } catch (error) {
      return null;
    }
  }

  // Determine if response indicates success
  isSuccessResponse(response) {
    const successIndicators = ['OK', 'SUCCESS', 'DONE', 'ACK', 'COMPLETED'];
    const errorIndicators = ['ERROR', 'FAIL', 'TIMEOUT', 'INVALID', 'DENIED'];

    const upperResponse = response.toUpperCase();

    if (successIndicators.some(indicator => upperResponse.includes(indicator))) {
      return true;
    }

    if (errorIndicators.some(indicator => upperResponse.includes(indicator))) {
      return false;
    }

    // Default to success if response is received
    return true;
  }

  // Get response status
  getResponseStatus(response) {
    const upperResponse = response.toUpperCase();

    if (upperResponse.includes('ERROR')) return 'failed';
    if (upperResponse.includes('TIMEOUT')) return 'timeout';
    if (upperResponse.includes('OFFLINE')) return 'device_offline';
    if (upperResponse.includes('INVALID')) return 'invalid_response';
    if (upperResponse.includes('OK') || upperResponse.includes('SUCCESS')) return 'success';

    return 'success'; // Default
  }

  // Publish command statistics update
  publishCommandUpdate(commandData) {
    if (!this.isConnected) return;

    const message = {
      type: 'command_update',
      data: commandData,
      timestamp: Date.now()
    };

    this.client.publish(this.topics.commands, JSON.stringify(message));
    
    // Update real-time cache
    this.realtimeCache.newCommands++;
  }

  // Publish device statistics update
  publishDeviceUpdate(deviceData) {
    if (!this.isConnected) return;

    const message = {
      type: 'device_update',
      data: deviceData,
      timestamp: Date.now()
    };

    this.client.publish(this.topics.devices, JSON.stringify(message));
  }

  // Publish user activity update
  publishUserUpdate(userData) {
    if (!this.isConnected) return;

    const message = {
      type: 'user_update',
      data: userData,
      timestamp: Date.now()
    };

    this.client.publish(this.topics.users, JSON.stringify(message));
  }

  // Publish real-time statistics
  publishRealtimeStats() {
    if (!this.isConnected) return;

    const message = {
      type: 'realtime_stats',
      data: { ...this.realtimeCache },
      timestamp: Date.now()
    };

    this.client.publish(this.topics.realtime, JSON.stringify(message));
  }

  // Handle refresh requests from clients
  async handleRefreshRequest(requestData) {
    try {
      const { filters = {} } = requestData;
      
      // Get current statistics based on filters
      const stats = await this.getCurrentStatistics(filters);
      
      // Publish updated statistics
      this.publishRealtimeStats();
      
      if (stats.commands) {
        this.publishCommandUpdate(stats.commands);
      }
      
      if (stats.devices) {
        this.publishDeviceUpdate(stats.devices);
      }
      
      if (stats.users) {
        this.publishUserUpdate(stats.users);
      }
    } catch (error) {
      console.error('Statistics MQTT: Error handling refresh request', error);
    }
  }

  // Get current statistics from database
  async getCurrentStatistics(filters = {}) {
    try {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      
      // Build match conditions
      let matchConditions = {
        createdAt: { $gte: oneHourAgo }
      };
      
      if (filters.deviceNumber) {
        matchConditions.deviceNumber = filters.deviceNumber;
      }
      
      if (filters.userId) {
        matchConditions.userId = filters.userId;
      }

      // Get active devices count
      const activeDevices = await LogModel.distinct('deviceNumber', matchConditions);
      
      // Get active users count
      const activeUsers = await LogModel.distinct('userId', matchConditions);
      
      // Update real-time cache
      this.realtimeCache.activeDevices = activeDevices.length;
      this.realtimeCache.activeUsers = activeUsers.length;
      
      return {
        commands: { recentCount: await LogModel.countDocuments(matchConditions) },
        devices: { activeCount: activeDevices.length },
        users: { activeCount: activeUsers.length }
      };
    } catch (error) {
      console.error('Statistics MQTT: Error getting current statistics', error);
      return {};
    }
  }

  // Setup periodic updates
  setupPeriodicUpdates() {
    // Publish real-time stats every 30 seconds
    setInterval(() => {
      this.publishRealtimeStats();
    }, 30000);

    // Reset counters every hour
    setInterval(() => {
      this.realtimeCache.newCommands = 0;
      this.realtimeCache.lastReset = Date.now();
    }, 3600000);

    // Update active counts every 5 minutes
    setInterval(async () => {
      try {
        await this.getCurrentStatistics();
      } catch (error) {
        console.error('Statistics MQTT: Error in periodic update', error);
      }
    }, 300000);
  }

  // Disconnect from MQTT
  disconnect() {
    if (this.client) {
      this.client.end();
      this.client = null;
    }
    this.isConnected = false;
  }
}

// Create singleton instance - DISABLED
// const statisticsMqttPublisher = new StatisticsMqttPublisher();

// Export a dummy object to prevent errors
module.exports = {
  publishCommandUpdate: () => {}, // No-op function
  publishDeviceUpdate: () => {}, // No-op function
  publishUserUpdate: () => {}, // No-op function
  disconnect: () => {}, // No-op function
  isConnected: false
};
