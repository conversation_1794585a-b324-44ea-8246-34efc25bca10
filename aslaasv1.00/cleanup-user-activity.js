const mongoose = require('mongoose');

// Define the schema directly since we might have import issues
const userActivityStatisticsSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  period: { type: String, enum: ['daily', 'weekly', 'monthly'], required: true },
  date: { type: Date, required: true },
  phoneNumber: { type: String, required: true },
  commandStats: {
    totalCommands: { type: Number, default: 0 },
    successfulCommands: { type: Number, default: 0 },
    failedCommands: { type: Number, default: 0 },
    successRate: { type: Number, default: 0 }
  },
  commandTypes: {
    power_on: { type: Number, default: 0 },
    power_off: { type: Number, default: 0 },
    lock: { type: Number, default: 0 },
    unlock: { type: Number, default: 0 },
    location: { type: Number, default: 0 },
    status: { type: Number, default: 0 },
    config: { type: Number, default: 0 },
    other: { type: Number, default: 0 }
  },
  deviceUsage: {
    devicesUsed: { type: Number, default: 0 },
    primaryDevice: { type: String, default: '' },
    deviceSwitches: { type: Number, default: 0 }
  },
  responseTimes: {
    avgResponseTime: { type: Number, default: null },
    fastestResponse: { type: Number, default: null },
    slowestResponse: { type: Number, default: null }
  },
  activityPattern: {
    mostActiveHour: { type: Number, default: null },
    leastActiveHour: { type: Number, default: null },
    weekdayActivity: { type: Number, default: 0 },
    weekendActivity: { type: Number, default: 0 }
  },
  lastActivity: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

const UserActivityStatistics = mongoose.model('userActivityStatistics', userActivityStatisticsSchema);

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/remotecarcontrol', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function cleanupUserActivityStatistics() {
  try {
    console.log('🧹 Starting cleanup of UserActivityStatistics...');
    
    // Find all documents with corrupted commandStats structure
    const corruptedDocs = await UserActivityStatistics.find({
      $or: [
        { 'commandStats.totalCommands': { $exists: false } },
        { 'commandStats.successfulCommands': { $exists: false } },
        { 'commandStats.failedCommands': { $exists: false } },
        { 'commandStats.successRate': { $exists: false } }
      ]
    });
    
    console.log(`Found ${corruptedDocs.length} corrupted documents`);
    
    for (const doc of corruptedDocs) {
      console.log(`Fixing document for user ${doc.userId}, date ${doc.date}`);
      
      // Fix the commandStats structure
      doc.commandStats = {
        totalCommands: doc.commandStats?.totalCommands || 0,
        successfulCommands: doc.commandStats?.successfulCommands || 0,
        failedCommands: doc.commandStats?.failedCommands || 0,
        successRate: doc.commandStats?.successRate || 0
      };
      
      // Ensure commandTypes structure is correct
      doc.commandTypes = {
        power_on: doc.commandTypes?.power_on || 0,
        power_off: doc.commandTypes?.power_off || 0,
        lock: doc.commandTypes?.lock || 0,
        unlock: doc.commandTypes?.unlock || 0,
        location: doc.commandTypes?.location || 0,
        status: doc.commandTypes?.status || 0,
        config: doc.commandTypes?.config || 0,
        other: doc.commandTypes?.other || 0
      };
      
      // Ensure phoneNumber is set
      if (!doc.phoneNumber) {
        doc.phoneNumber = 'unknown';
      }
      
      await doc.save();
      console.log(`✅ Fixed document for user ${doc.userId}`);
    }
    
    console.log('🎉 Cleanup completed successfully!');
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    process.exit(1);
  }
}

cleanupUserActivityStatistics();
