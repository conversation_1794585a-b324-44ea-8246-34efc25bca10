const mongoose = require('mongoose');
const LogModel = require('./models/log');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/aslaa', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

const createSampleData = async () => {
  try {
    console.log('Creating sample statistics data...');

    // Sample device numbers and user IDs
    const deviceNumbers = ['12345', '67890', '11111', '22222', '33333'];
    const userIds = ['user1', 'user2', 'user3', 'user4', 'user5'];
    const commandTypes = ['power_on', 'power_off', 'lock', 'unlock', 'location', 'status'];
    const commands = [
      'POWER_ON', 'POWER_OFF', 'LOCK', 'UNLOCK', 
      'GET_LOCATION', 'GET_STATUS', 'SET_CONFIG', 'CHECK'
    ];
    const responses = [
      'OK', 'SUCCESS', 'DONE', 'ACK', 'ERROR', 'TIMEOUT', 'INVALID'
    ];

    const sampleLogs = [];
    const now = new Date();

    // Create 100 sample log entries over the last 30 days
    for (let i = 0; i < 100; i++) {
      const daysAgo = Math.floor(Math.random() * 30);
      const hoursAgo = Math.floor(Math.random() * 24);
      const minutesAgo = Math.floor(Math.random() * 60);
      
      const sentTime = new Date(now);
      sentTime.setDate(sentTime.getDate() - daysAgo);
      sentTime.setHours(sentTime.getHours() - hoursAgo);
      sentTime.setMinutes(sentTime.getMinutes() - minutesAgo);

      const receiveTime = new Date(sentTime);
      receiveTime.setSeconds(receiveTime.getSeconds() + Math.floor(Math.random() * 10) + 1);

      const deviceNumber = deviceNumbers[Math.floor(Math.random() * deviceNumbers.length)];
      const userId = userIds[Math.floor(Math.random() * userIds.length)];
      const command = commands[Math.floor(Math.random() * commands.length)];
      const commandType = commandTypes[Math.floor(Math.random() * commandTypes.length)];
      const response = responses[Math.floor(Math.random() * responses.length)];
      const success = !['ERROR', 'TIMEOUT', 'INVALID'].includes(response);
      const responseTime = receiveTime.getTime() - sentTime.getTime();

      const log = {
        user: userId,
        userId: userId,
        deviceNumber: deviceNumber,
        command: command,
        commandType: commandType,
        sent: "yes",
        success: success,
        sentTime: sentTime,
        receiveTime: receiveTime,
        responseTime: responseTime,
        response: response,
        responseType: "MQTT",
        deviceOnline: success,
        responseStatus: success ? 'success' : (response === 'TIMEOUT' ? 'timeout' : 'failed'),
        failureReason: success ? null : `Command failed: ${response}`,
        message: `Sample command ${i + 1}`,
        createdAt: sentTime,
        updatedAt: receiveTime
      };

      sampleLogs.push(log);
    }

    // Clear existing sample data
    await LogModel.deleteMany({ message: { $regex: /^Sample command/ } });
    console.log('Cleared existing sample data');

    // Insert new sample data
    await LogModel.insertMany(sampleLogs);
    console.log(`Created ${sampleLogs.length} sample log entries`);

    // Create some statistics summary
    const stats = await LogModel.aggregate([
      { $match: { message: { $regex: /^Sample command/ } } },
      {
        $group: {
          _id: null,
          totalCommands: { $sum: 1 },
          successfulCommands: { $sum: { $cond: ['$success', 1, 0] } },
          failedCommands: { $sum: { $cond: ['$success', 0, 1] } },
          avgResponseTime: { $avg: '$responseTime' },
          uniqueDevices: { $addToSet: '$deviceNumber' },
          uniqueUsers: { $addToSet: '$userId' }
        }
      }
    ]);

    if (stats.length > 0) {
      const stat = stats[0];
      console.log('\n📊 Sample Data Summary:');
      console.log(`Total Commands: ${stat.totalCommands}`);
      console.log(`Successful: ${stat.successfulCommands}`);
      console.log(`Failed: ${stat.failedCommands}`);
      console.log(`Success Rate: ${((stat.successfulCommands / stat.totalCommands) * 100).toFixed(1)}%`);
      console.log(`Average Response Time: ${stat.avgResponseTime.toFixed(0)}ms`);
      console.log(`Unique Devices: ${stat.uniqueDevices.length}`);
      console.log(`Unique Users: ${stat.uniqueUsers.length}`);
    }

    console.log('\n✅ Sample data created successfully!');
    console.log('You can now view the statistics dashboard.');

  } catch (error) {
    console.error('Error creating sample data:', error);
  } finally {
    mongoose.connection.close();
  }
};

createSampleData();
