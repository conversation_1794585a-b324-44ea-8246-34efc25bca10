describe('License Bonus System', () => {

  describe('License Bonus Calculation Logic', () => {
    test('should calculate correct bonus for different license durations', () => {
      const pricePerMonth = 5000; // Default price per month
      const bonusDays = 30; // 1-month bonus

      const testCases = [
        { cost: 5000, expectedOriginalDays: 30, expectedBonusDays: 0, expectedTotalDays: 30 }, // 1 month - NO bonus
        { cost: 10000, expectedOriginalDays: 60, expectedBonusDays: 0, expectedTotalDays: 60 }, // 2 months - NO bonus
        { cost: 15000, expectedOriginalDays: 90, expectedBonusDays: 0, expectedTotalDays: 90 }, // 3 months - NO bonus
        { cost: 25000, expectedOriginalDays: 150, expectedBonusDays: 0, expectedTotalDays: 150 }, // 5 months - NO bonus
        { cost: 30000, expectedOriginalDays: 180, expectedBonusDays: 30, expectedTotalDays: 210 }, // 6 months + 1 month bonus = 7 months total
        { cost: 35000, expectedOriginalDays: 210, expectedBonusDays: 30, expectedTotalDays: 240 }, // 7 months + 1 month bonus = 8 months total
      ];

      testCases.forEach(testCase => {
        const months = testCase.cost / pricePerMonth;
        const originalDays = months * 30;

        // Apply bonus only for 6+ months
        const actualBonusDays = months >= 6 ? 30 : 0;
        const totalDays = originalDays + actualBonusDays;

        expect(originalDays).toBe(testCase.expectedOriginalDays);
        expect(actualBonusDays).toBe(testCase.expectedBonusDays);
        expect(totalDays).toBe(testCase.expectedTotalDays);
      });
    });

    test('should apply bonus only for 6+ months across different extension methods', () => {
      // Test manual user expiry update logic with 6-month minimum
      const userExpiryTestCases = [
        { originalDays: 30, months: 1, expectedBonus: 0, expectedTotal: 30 }, // 1 month - no bonus
        { originalDays: 60, months: 2, expectedBonus: 0, expectedTotal: 60 }, // 2 months - no bonus
        { originalDays: 150, months: 5, expectedBonus: 0, expectedTotal: 150 }, // 5 months - no bonus
        { originalDays: 180, months: 6, expectedBonus: 30, expectedTotal: 210 }, // 6 months - gets bonus
        { originalDays: 240, months: 8, expectedBonus: 30, expectedTotal: 270 } // 8 months - gets bonus
      ];

      userExpiryTestCases.forEach(testCase => {
        const months = testCase.originalDays / 30;
        const bonusDays = months >= 6 ? 30 : 0;
        const totalDays = testCase.originalDays + bonusDays;

        expect(months).toBe(testCase.months);
        expect(bonusDays).toBe(testCase.expectedBonus);
        expect(totalDays).toBe(testCase.expectedTotal);
      });
    });
  });

  describe('Specific User Scenarios', () => {
    test('should give 1-month bonus when user extends 6 months or more', () => {
      const pricePerMonth = 5000;

      // Test 6 months - should get bonus
      const userPayment6 = 30000; // 6 months worth
      const months6 = userPayment6 / pricePerMonth; // 6 months
      const originalDays6 = months6 * 30; // 180 days
      const bonusDays6 = months6 >= 6 ? 30 : 0; // 1 month bonus
      const totalDays6 = originalDays6 + bonusDays6; // 210 days

      expect(months6).toBe(6); // User paid for 6 months
      expect(originalDays6).toBe(180); // 6 months = 180 days
      expect(bonusDays6).toBe(30); // 1 month bonus = 30 days
      expect(totalDays6).toBe(210); // Total = 7 months (6 paid + 1 bonus)

      // Test 5 months - should NOT get bonus
      const userPayment5 = 25000; // 5 months worth
      const months5 = userPayment5 / pricePerMonth; // 5 months
      const originalDays5 = months5 * 30; // 150 days
      const bonusDays5 = months5 >= 6 ? 30 : 0; // No bonus
      const totalDays5 = originalDays5 + bonusDays5; // 150 days

      expect(months5).toBe(5); // User paid for 5 months
      expect(originalDays5).toBe(150); // 5 months = 150 days
      expect(bonusDays5).toBe(0); // No bonus
      expect(totalDays5).toBe(150); // Total = 5 months (no bonus)
    });
  });

  describe('License Bonus Constants', () => {
    test('should have correct bonus constants', () => {
      const BONUS_DAYS = 30;
      const BONUS_REASON = '1-month extension bonus';

      expect(BONUS_DAYS).toBe(30);
      expect(BONUS_REASON).toBe('1-month extension bonus');
    });

    test('should calculate expiry dates correctly with bonus', () => {
      const currentTime = new Date('2024-01-01T00:00:00Z').getTime();
      const originalDays = 60;
      const bonusDays = 30;
      const totalDays = originalDays + bonusDays;

      const expectedExpiry = new Date(currentTime + totalDays * 24 * 60 * 60 * 1000);
      const calculatedExpiry = new Date(currentTime + (originalDays + bonusDays) * 24 * 60 * 60 * 1000);

      expect(expectedExpiry.getTime()).toBe(calculatedExpiry.getTime());
      expect(totalDays).toBe(90);
    });
  });
});
