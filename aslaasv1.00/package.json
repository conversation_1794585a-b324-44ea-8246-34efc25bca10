{"name": "remotecarcontrol", "version": "1.0.0", "description": "This is for online remote car control", "main": "server.js", "scripts": {"test": "jest", "test:bonus": "jest tests/licenseBonus.test.js", "test:2fa": "node scripts/test-2fa.js", "start": "nodemon server.js"}, "devDependencies": {"jest": "^30.0.5", "mongodb": "^6.17.0", "mqtt": "^5.13.2", "nodemon": "^2.0.15", "supertest": "^7.1.4"}, "dependencies": {"aws-sdk": "^2.1292.0", "axios": "^0.26.0", "bcryptjs": "^2.4.3", "body-parser": "^1.19.1", "concurrently": "^6.5.1", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^10.0.0", "express": "^4.18.2", "express-rate-limit": "^7.5.1", "firebase-admin": "^11.5.0", "form-data": "^4.0.0", "is-empty": "^1.2.0", "jsonwebtoken": "^8.5.1", "license-key-generator": "^1.1.2", "moment": "^2.29.1", "mongoose": "^6.1.4", "multer": "^1.4.4", "multer-s3": "^3.0.1", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "npm": "^9.6.2", "numeral": "^2.0.6", "otp-generator": "^4.0.0", "passport": "^0.5.2", "passport-jwt": "^4.0.0", "path": "^0.12.7", "pusher": "^5.0.1", "qrcode": "^1.5.4", "qrcode.react": "^4.2.0", "react-otp-input": "^3.1.1", "request": "^2.88.2", "socket.io": "^4.4.1", "speakeasy": "^2.0.0", "validator": "^13.7.0", "web-vitals": "^2.1.2"}, "repository": {"type": "git", "url": "git+https://github.com/alexidr9116/remotecarcontrol.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/alexidr9116/remotecarcontrol.git/issues"}, "homepage": "https://github.com/alexidr9116/remotecarcontrol.git#readme"}