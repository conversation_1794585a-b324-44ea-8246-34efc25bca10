const mongoose = require("mongoose");
const Schema = mongoose.Schema;

// Create Schema for user activity statistics
const UserActivityStatisticsSchema = new Schema({
    // User reference
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    
    // User phone number for easier querying
    phoneNumber: {
        type: String,
        required: true
    },
    
    // Time period this statistic represents
    period: {
        type: String,
        enum: ['daily', 'weekly', 'monthly', 'all'],
        required: true
    },
    
    // Date this statistic represents (start of day/week/month)
    date: {
        type: Date,
        required: true
    },
    
    // Command statistics for this user
    commandStats: {
        totalCommands: { type: Number, default: 0 },
        successfulCommands: { type: Number, default: 0 },
        failedCommands: { type: Number, default: 0 },
        successRate: { type: Number, default: 0 } // percentage
    },
    
    // Command type breakdown for this user
    commandTypes: {
        power_on: { type: Number, default: 0 },
        power_off: { type: Number, default: 0 },
        lock: { type: Number, default: 0 },
        unlock: { type: Number, default: 0 },
        location: { type: Number, default: 0 },
        status: { type: Number, default: 0 },
        config: { type: Number, default: 0 },
        other: { type: Number, default: 0 }
    },
    
    // Device usage statistics
    deviceUsage: {
        devicesUsed: { type: Number, default: 0 },
        primaryDevice: { type: String, default: "" },
        deviceSwitches: { type: Number, default: 0 }
    },
    
    // Response time statistics for this user
    responseTimes: {
        avgResponseTime: { type: Number, default: null },
        fastestResponse: { type: Number, default: null },
        slowestResponse: { type: Number, default: null }
    },
    
    // Activity patterns
    activityPattern: {
        mostActiveHour: { type: Number, default: null }, // 0-23
        leastActiveHour: { type: Number, default: null }, // 0-23
        weekdayActivity: { type: Number, default: 0 },
        weekendActivity: { type: Number, default: 0 }
    },
    
    // Last activity timestamp
    lastActivity: {
        type: Date,
        default: null
    },
    
    // Last updated timestamp
    updatedAt: {
        type: Date,
        default: Date.now
    }
}, { timestamps: true });

// Create compound indexes for efficient querying
UserActivityStatisticsSchema.index({ userId: 1, period: 1, date: 1 }, { unique: true });
UserActivityStatisticsSchema.index({ phoneNumber: 1, period: 1, date: 1 });
UserActivityStatisticsSchema.index({ 'commandStats.totalCommands': -1 });

module.exports = UserActivityStatistics = mongoose.model("userActivityStatistics", UserActivityStatisticsSchema);
