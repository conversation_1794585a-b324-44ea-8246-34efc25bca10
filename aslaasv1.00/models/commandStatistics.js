const mongoose = require("mongoose");
const Schema = mongoose.Schema;

// Create Schema for aggregated command statistics
const CommandStatisticsSchema = new Schema({
    // Time period this statistic represents
    period: {
        type: String,
        enum: ['daily', 'weekly', 'monthly', 'all'],
        required: true
    },
    
    // Date this statistic represents (start of day/week/month)
    date: {
        type: Date,
        required: true
    },
    
    // Total commands sent in this period
    totalCommands: {
        type: Number,
        default: 0
    },
    
    // Success/failure counts
    successCount: {
        type: Number,
        default: 0
    },
    
    failureCount: {
        type: Number,
        default: 0
    },
    
    // Command type breakdown
    commandTypes: {
        power_on: { type: Number, default: 0 },
        power_off: { type: Number, default: 0 },
        lock: { type: Number, default: 0 },
        unlock: { type: Number, default: 0 },
        location: { type: Number, default: 0 },
        status: { type: Number, default: 0 },
        config: { type: Number, default: 0 },
        other: { type: Number, default: 0 }
    },
    
    // Failure reasons breakdown
    failureReasons: {
        timeout: { type: Number, default: 0 },
        device_offline: { type: Number, default: 0 },
        invalid_response: { type: Number, default: 0 },
        other: { type: Number, default: 0 }
    },
    
    // Response time statistics (in milliseconds)
    responseTimes: {
        min: { type: Number, default: null },
        max: { type: Number, default: null },
        avg: { type: Number, default: null },
        median: { type: Number, default: null }
    },
    
    // Device statistics
    deviceStats: {
        totalDevices: { type: Number, default: 0 },
        onlineDevices: { type: Number, default: 0 },
        offlineDevices: { type: Number, default: 0 }
    },
    
    // Last updated timestamp
    updatedAt: {
        type: Date,
        default: Date.now
    }
}, { timestamps: true });

// Create compound index for efficient querying
CommandStatisticsSchema.index({ period: 1, date: 1 }, { unique: true });

// Check if model already exists to prevent overwrite error
module.exports = mongoose.models.commandStatistics || mongoose.model("commandStatistics", CommandStatisticsSchema);
