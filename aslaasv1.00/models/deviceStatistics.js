const mongoose = require("mongoose");
const Schema = mongoose.Schema;

// Create Schema for device performance statistics
const DeviceStatisticsSchema = new Schema({
    // Device reference
    deviceNumber: {
        type: String,
        required: true
    },
    
    // Device type
    deviceType: {
        type: String,
        default: '4g'
    },
    
    // Time period this statistic represents
    period: {
        type: String,
        enum: ['daily', 'weekly', 'monthly', 'all'],
        required: true
    },
    
    // Date this statistic represents (start of day/week/month)
    date: {
        type: Date,
        required: true
    },
    
    // Command statistics for this device
    commandStats: {
        totalCommands: { type: Number, default: 0 },
        successfulCommands: { type: Number, default: 0 },
        failedCommands: { type: Number, default: 0 },
        successRate: { type: Number, default: 0 }, // percentage
        timeoutCommands: { type: Number, default: 0 }
    },
    
    // Response performance
    responsePerformance: {
        avgResponseTime: { type: Number, default: null }, // milliseconds
        minResponseTime: { type: Number, default: null },
        maxResponseTime: { type: Number, default: null },
        responseTimeDistribution: {
            fast: { type: Number, default: 0 }, // < 1000ms
            medium: { type: Number, default: 0 }, // 1000-5000ms
            slow: { type: Number, default: 0 }, // > 5000ms
            timeout: { type: Number, default: 0 } // no response
        }
    },
    
    // Availability statistics
    availability: {
        totalUptime: { type: Number, default: 0 }, // minutes
        totalDowntime: { type: Number, default: 0 }, // minutes
        uptimePercentage: { type: Number, default: 100 },
        lastOnlineTime: { type: Date, default: null },
        lastOfflineTime: { type: Date, default: null }
    },
    
    // Signal strength statistics
    signalStats: {
        avgSignalStrength: { type: Number, default: null },
        minSignalStrength: { type: Number, default: null },
        maxSignalStrength: { type: Number, default: null },
        signalQuality: {
            excellent: { type: Number, default: 0 }, // > -70 dBm
            good: { type: Number, default: 0 }, // -70 to -85 dBm
            fair: { type: Number, default: 0 }, // -85 to -100 dBm
            poor: { type: Number, default: 0 } // < -100 dBm
        }
    },
    
    // Command type breakdown for this device
    commandTypes: {
        power_on: { type: Number, default: 0 },
        power_off: { type: Number, default: 0 },
        lock: { type: Number, default: 0 },
        unlock: { type: Number, default: 0 },
        location: { type: Number, default: 0 },
        status: { type: Number, default: 0 },
        config: { type: Number, default: 0 },
        other: { type: Number, default: 0 }
    },
    
    // Failure analysis
    failureAnalysis: {
        timeoutRate: { type: Number, default: 0 }, // percentage
        offlineRate: { type: Number, default: 0 }, // percentage
        invalidResponseRate: { type: Number, default: 0 }, // percentage
        mostCommonFailure: { type: String, default: "" }
    },
    
    // Usage patterns
    usagePattern: {
        peakUsageHour: { type: Number, default: null }, // 0-23
        lowUsageHour: { type: Number, default: null }, // 0-23
        weekdayUsage: { type: Number, default: 0 },
        weekendUsage: { type: Number, default: 0 }
    },
    
    // Last updated timestamp
    updatedAt: {
        type: Date,
        default: Date.now
    }
}, { timestamps: true });

// Create compound indexes for efficient querying
DeviceStatisticsSchema.index({ deviceNumber: 1, period: 1, date: 1 }, { unique: true });
DeviceStatisticsSchema.index({ 'commandStats.totalCommands': -1 });
DeviceStatisticsSchema.index({ 'responsePerformance.avgResponseTime': 1 });
DeviceStatisticsSchema.index({ 'availability.uptimePercentage': -1 });

module.exports = DeviceStatistics = mongoose.model("deviceStatistics", DeviceStatisticsSchema);
