(this.webpackJsonpclient=this.webpackJsonpclient||[]).push([[36],{1142:function(e,t,n){"use strict";var r=n(11),o=n(3),i=n(0),a=n(42),c=n(558),s=n(55),l=n(49),u=n(69),p=n(624),d=n(230),f=n(674),b=n(559),h=n(525);function y(e){return Object(h.a)("MuiLink",e)}var m=Object(b.a)("MuiLink",["root","underlineNone","underlineHover","underlineAlways","button","focusVisible"]),j=n(13),v=n(566);const O={primary:"primary.main",textPrimary:"text.primary",secondary:"secondary.main",textSecondary:"text.secondary",error:"error.main"};var g=e=>{let{theme:t,ownerState:n}=e;const r=(e=>O[e]||e)(n.color),o=Object(j.b)(t,"palette.".concat(r),!1)||n.color,i=Object(j.b)(t,"palette.".concat(r,"Channel"));return"vars"in t&&i?"rgba(".concat(i," / 0.4)"):Object(v.a)(o,.4)},x=n(2);const w=["className","color","component","onBlur","onFocus","TypographyClasses","underline","variant","sx"],P=Object(l.a)(f.a,{name:"MuiLink",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,t["underline".concat(Object(s.a)(n.underline))],"button"===n.component&&t.button]}})((e=>{let{theme:t,ownerState:n}=e;return Object(o.a)({},"none"===n.underline&&{textDecoration:"none"},"hover"===n.underline&&{textDecoration:"none","&:hover":{textDecoration:"underline"}},"always"===n.underline&&Object(o.a)({textDecoration:"underline"},"inherit"!==n.color&&{textDecorationColor:g({theme:t,ownerState:n})},{"&:hover":{textDecorationColor:"inherit"}}),"button"===n.component&&{position:"relative",WebkitTapHighlightColor:"transparent",backgroundColor:"transparent",outline:0,border:0,margin:0,borderRadius:0,padding:0,cursor:"pointer",userSelect:"none",verticalAlign:"middle",MozAppearance:"none",WebkitAppearance:"none","&::-moz-focus-inner":{borderStyle:"none"},["&.".concat(m.focusVisible)]:{outline:"auto"}})})),S=i.forwardRef((function(e,t){const n=Object(u.a)({props:e,name:"MuiLink"}),{className:l,color:f="primary",component:b="a",onBlur:h,onFocus:m,TypographyClasses:j,underline:v="always",variant:g="inherit",sx:S}=n,k=Object(r.a)(n,w),{isFocusVisibleRef:T,onBlur:I,onFocus:R,ref:E}=Object(p.a)(),[C,L]=i.useState(!1),M=Object(d.a)(t,E),N=Object(o.a)({},n,{color:f,component:b,focusVisible:C,underline:v,variant:g}),D=(e=>{const{classes:t,component:n,focusVisible:r,underline:o}=e,i={root:["root","underline".concat(Object(s.a)(o)),"button"===n&&"button",r&&"focusVisible"]};return Object(c.a)(i,y,t)})(N);return Object(x.jsx)(P,Object(o.a)({color:f,className:Object(a.a)(D.root,l),classes:j,component:b,onBlur:e=>{I(e),!1===T.current&&L(!1),h&&h(e)},onFocus:e=>{R(e),!0===T.current&&L(!0),m&&m(e)},ref:M,ownerState:N,variant:g,sx:[...Object.keys(O).includes(f)?[]:[{color:f}],...Array.isArray(S)?S:[S]]},k))}));t.a=S},1227:function(e,t,n){(()=>{var t={296:(e,t,n)=>{var r=/^\s+|\s+$/g,o=/^[-+]0x[0-9a-f]+$/i,i=/^0b[01]+$/i,a=/^0o[0-7]+$/i,c=parseInt,s="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g,l="object"==typeof self&&self&&self.Object===Object&&self,u=s||l||Function("return this")(),p=Object.prototype.toString,d=Math.max,f=Math.min,b=function(){return u.Date.now()};function h(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function y(e){if("number"==typeof e)return e;if(function(e){return"symbol"==typeof e||function(e){return!!e&&"object"==typeof e}(e)&&"[object Symbol]"==p.call(e)}(e))return NaN;if(h(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=h(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(r,"");var n=i.test(e);return n||a.test(e)?c(e.slice(2),n?2:8):o.test(e)?NaN:+e}e.exports=function(e,t,n){var r,o,i,a,c,s,l=0,u=!1,p=!1,m=!0;if("function"!=typeof e)throw new TypeError("Expected a function");function j(t){var n=r,i=o;return r=o=void 0,l=t,a=e.apply(i,n)}function v(e){return l=e,c=setTimeout(g,t),u?j(e):a}function O(e){var n=e-s;return void 0===s||n>=t||n<0||p&&e-l>=i}function g(){var e=b();if(O(e))return x(e);c=setTimeout(g,function(e){var n=t-(e-s);return p?f(n,i-(e-l)):n}(e))}function x(e){return c=void 0,m&&r?j(e):(r=o=void 0,a)}function w(){var e=b(),n=O(e);if(r=arguments,o=this,s=e,n){if(void 0===c)return v(s);if(p)return c=setTimeout(g,t),j(s)}return void 0===c&&(c=setTimeout(g,t)),a}return t=y(t)||0,h(n)&&(u=!!n.leading,i=(p="maxWait"in n)?d(y(n.maxWait)||0,t):i,m="trailing"in n?!!n.trailing:m),w.cancel=function(){void 0!==c&&clearTimeout(c),l=0,r=s=o=c=void 0},w.flush=function(){return void 0===c?a:x(b())},w}},96:(e,t,n)=>{var r="Expected a function",o=/^\s+|\s+$/g,i=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,c=/^0o[0-7]+$/i,s=parseInt,l="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g,u="object"==typeof self&&self&&self.Object===Object&&self,p=l||u||Function("return this")(),d=Object.prototype.toString,f=Math.max,b=Math.min,h=function(){return p.Date.now()};function y(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function m(e){if("number"==typeof e)return e;if(function(e){return"symbol"==typeof e||function(e){return!!e&&"object"==typeof e}(e)&&"[object Symbol]"==d.call(e)}(e))return NaN;if(y(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=y(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(o,"");var n=a.test(e);return n||c.test(e)?s(e.slice(2),n?2:8):i.test(e)?NaN:+e}e.exports=function(e,t,n){var o=!0,i=!0;if("function"!=typeof e)throw new TypeError(r);return y(n)&&(o="leading"in n?!!n.leading:o,i="trailing"in n?!!n.trailing:i),function(e,t,n){var o,i,a,c,s,l,u=0,p=!1,d=!1,j=!0;if("function"!=typeof e)throw new TypeError(r);function v(t){var n=o,r=i;return o=i=void 0,u=t,c=e.apply(r,n)}function O(e){return u=e,s=setTimeout(x,t),p?v(e):c}function g(e){var n=e-l;return void 0===l||n>=t||n<0||d&&e-u>=a}function x(){var e=h();if(g(e))return w(e);s=setTimeout(x,function(e){var n=t-(e-l);return d?b(n,a-(e-u)):n}(e))}function w(e){return s=void 0,j&&o?v(e):(o=i=void 0,c)}function P(){var e=h(),n=g(e);if(o=arguments,i=this,l=e,n){if(void 0===s)return O(l);if(d)return s=setTimeout(x,t),v(l)}return void 0===s&&(s=setTimeout(x,t)),c}return t=m(t)||0,y(n)&&(p=!!n.leading,a=(d="maxWait"in n)?f(m(n.maxWait)||0,t):a,j="trailing"in n?!!n.trailing:j),P.cancel=function(){void 0!==s&&clearTimeout(s),u=0,o=l=i=s=void 0},P.flush=function(){return void 0===s?c:w(h())},P}(e,t,{leading:o,maxWait:t,trailing:i})}},703:(e,t,n)=>{"use strict";var r=n(414);function o(){}function i(){}i.resetWarningCache=o,e.exports=function(){function e(e,t,n,o,i,a){if(a!==r){var c=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function t(){return e}e.isRequired=e;var n={array:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:o};return n.PropTypes=n,n}},697:(e,t,n)=>{e.exports=n(703)()},414:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"}},r={};function o(e){var n=r[e];if(void 0!==n)return n.exports;var i=r[e]={exports:{}};return t[e](i,i.exports,o),i.exports}o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},o.d=(e,t)=>{for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var i={};(()=>{"use strict";o.r(i),o.d(i,{LazyLoadComponent:()=>U,LazyLoadImage:()=>oe,trackWindowScroll:()=>N});const e=n(0);var t=o.n(e),r=o(697);const a=n(52);var c=o.n(a);function s(){return"undefined"!=typeof window&&"IntersectionObserver"in window&&"isIntersecting"in window.IntersectionObserverEntry.prototype}function l(e){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function p(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function d(e,t){return(d=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function f(e,t){if(t&&("object"===l(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function b(e){return(b=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var h=function(e){e.forEach((function(e){e.isIntersecting&&e.target.onVisible()}))},y={},m=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&d(e,t)}(a,e);var n,r,o,i=(r=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=b(r);if(o){var n=b(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return f(this,e)});function a(e){var t;if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,a),(t=i.call(this,e)).supportsObserver=!e.scrollPosition&&e.useIntersectionObserver&&s(),t.supportsObserver){var n=e.threshold;t.observer=function(e){return y[e]=y[e]||new IntersectionObserver(h,{rootMargin:e+"px"}),y[e]}(n)}return t}return(n=[{key:"componentDidMount",value:function(){this.placeholder&&this.observer&&(this.placeholder.onVisible=this.props.onVisible,this.observer.observe(this.placeholder)),this.supportsObserver||this.updateVisibility()}},{key:"componentWillUnmount",value:function(){this.observer&&this.placeholder&&this.observer.unobserve(this.placeholder)}},{key:"componentDidUpdate",value:function(){this.supportsObserver||this.updateVisibility()}},{key:"getPlaceholderBoundingBox",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props.scrollPosition,t=this.placeholder.getBoundingClientRect(),n=c().findDOMNode(this.placeholder).style,r={left:parseInt(n.getPropertyValue("margin-left"),10)||0,top:parseInt(n.getPropertyValue("margin-top"),10)||0};return{bottom:e.y+t.bottom+r.top,left:e.x+t.left+r.left,right:e.x+t.right+r.left,top:e.y+t.top+r.top}}},{key:"isPlaceholderInViewport",value:function(){if("undefined"==typeof window||!this.placeholder)return!1;var e=this.props,t=e.scrollPosition,n=e.threshold,r=this.getPlaceholderBoundingBox(t),o=t.y+window.innerHeight,i=t.x,a=t.x+window.innerWidth,c=t.y;return Boolean(c-n<=r.bottom&&o+n>=r.top&&i-n<=r.right&&a+n>=r.left)}},{key:"updateVisibility",value:function(){this.isPlaceholderInViewport()&&this.props.onVisible()}},{key:"render",value:function(){var e=this,n=this.props,r=n.className,o=n.height,i=n.placeholder,a=n.style,c=n.width;if(i&&"function"!=typeof i.type)return t().cloneElement(i,{ref:function(t){return e.placeholder=t}});var s=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({display:"inline-block"},a);return void 0!==c&&(s.width=c),void 0!==o&&(s.height=o),t().createElement("span",{className:r,ref:function(t){return e.placeholder=t},style:s},i)}}])&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(a.prototype,n),a}(t().Component);m.propTypes={onVisible:r.PropTypes.func.isRequired,className:r.PropTypes.string,height:r.PropTypes.oneOfType([r.PropTypes.number,r.PropTypes.string]),placeholder:r.PropTypes.element,threshold:r.PropTypes.number,useIntersectionObserver:r.PropTypes.bool,scrollPosition:r.PropTypes.shape({x:r.PropTypes.number.isRequired,y:r.PropTypes.number.isRequired}),width:r.PropTypes.oneOfType([r.PropTypes.number,r.PropTypes.string])},m.defaultProps={className:"",placeholder:null,threshold:100,useIntersectionObserver:!0};const j=m;var v=o(296),O=o.n(v),g=o(96),x=o.n(g),w=function(e){var t=getComputedStyle(e,null);return t.getPropertyValue("overflow")+t.getPropertyValue("overflow-y")+t.getPropertyValue("overflow-x")};const P=function(e){if(!(e instanceof HTMLElement))return window;for(var t=e;t&&t instanceof HTMLElement;){if(/(scroll|auto)/.test(w(t)))return t;t=t.parentNode}return window};function S(e){return(S="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var k=["delayMethod","delayTime"];function T(){return(T=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function I(e,t){return(I=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function R(e,t){if(t&&("object"===S(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return E(e)}function E(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function C(e){return(C=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var L=function(){return"undefined"==typeof window?0:window.scrollX||window.pageXOffset},M=function(){return"undefined"==typeof window?0:window.scrollY||window.pageYOffset};const N=function(e){var n=function(n){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&I(e,t)}(l,n);var r,o,i,a=(o=l,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=C(o);if(i){var n=C(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return R(this,e)});function l(e){var n;if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),(n=a.call(this,e)).useIntersectionObserver=e.useIntersectionObserver&&s(),n.useIntersectionObserver)return R(n);var r=n.onChangeScroll.bind(E(n));return"debounce"===e.delayMethod?n.delayedScroll=O()(r,e.delayTime):"throttle"===e.delayMethod&&(n.delayedScroll=x()(r,e.delayTime)),n.state={scrollPosition:{x:L(),y:M()}},n.baseComponentRef=t().createRef(),n}return(r=[{key:"componentDidMount",value:function(){this.addListeners()}},{key:"componentWillUnmount",value:function(){this.removeListeners()}},{key:"componentDidUpdate",value:function(){"undefined"==typeof window||this.useIntersectionObserver||P(c().findDOMNode(this.baseComponentRef.current))!==this.scrollElement&&(this.removeListeners(),this.addListeners())}},{key:"addListeners",value:function(){"undefined"==typeof window||this.useIntersectionObserver||(this.scrollElement=P(c().findDOMNode(this.baseComponentRef.current)),this.scrollElement.addEventListener("scroll",this.delayedScroll,{passive:!0}),window.addEventListener("resize",this.delayedScroll,{passive:!0}),this.scrollElement!==window&&window.addEventListener("scroll",this.delayedScroll,{passive:!0}))}},{key:"removeListeners",value:function(){"undefined"==typeof window||this.useIntersectionObserver||(this.scrollElement.removeEventListener("scroll",this.delayedScroll),window.removeEventListener("resize",this.delayedScroll),this.scrollElement!==window&&window.removeEventListener("scroll",this.delayedScroll))}},{key:"onChangeScroll",value:function(){this.useIntersectionObserver||this.setState({scrollPosition:{x:L(),y:M()}})}},{key:"render",value:function(){var n=this.props,r=(n.delayMethod,n.delayTime,function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(n,k)),o=this.useIntersectionObserver?null:this.state.scrollPosition;return t().createElement(e,T({forwardRef:this.baseComponentRef,scrollPosition:o},r))}}])&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(l.prototype,r),l}(t().Component);return n.propTypes={delayMethod:r.PropTypes.oneOf(["debounce","throttle"]),delayTime:r.PropTypes.number,useIntersectionObserver:r.PropTypes.bool},n.defaultProps={delayMethod:"throttle",delayTime:300,useIntersectionObserver:!0},n};function D(e){return(D="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _(e,t){return(_=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function B(e,t){if(t&&("object"===D(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function z(e){return(z=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var W=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&_(e,t)}(a,e);var n,r,o,i=(r=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=z(r);if(o){var n=z(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return B(this,e)});function a(e){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,a),i.call(this,e)}return(n=[{key:"render",value:function(){return t().createElement(j,this.props)}}])&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(a.prototype,n),a}(t().Component);const V=N(W);function F(e){return(F="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function G(e,t){return(G=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function Y(e,t){if(t&&("object"===F(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return A(e)}function A(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function H(e){return(H=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var X=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&G(e,t)}(a,e);var n,r,o,i=(r=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=H(r);if(o){var n=H(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return Y(this,e)});function a(e){var t;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,a),t=i.call(this,e);var n=e.afterLoad,r=e.beforeLoad,o=e.scrollPosition,c=e.visibleByDefault;return t.state={visible:c},c&&(r(),n()),t.onVisible=t.onVisible.bind(A(t)),t.isScrollTracked=Boolean(o&&Number.isFinite(o.x)&&o.x>=0&&Number.isFinite(o.y)&&o.y>=0),t}return(n=[{key:"componentDidUpdate",value:function(e,t){t.visible!==this.state.visible&&this.props.afterLoad()}},{key:"onVisible",value:function(){this.props.beforeLoad(),this.setState({visible:!0})}},{key:"render",value:function(){if(this.state.visible)return this.props.children;var e=this.props,n=e.className,r=e.delayMethod,o=e.delayTime,i=e.height,a=e.placeholder,c=e.scrollPosition,l=e.style,u=e.threshold,p=e.useIntersectionObserver,d=e.width;return this.isScrollTracked||p&&s()?t().createElement(j,{className:n,height:i,onVisible:this.onVisible,placeholder:a,scrollPosition:c,style:l,threshold:u,useIntersectionObserver:p,width:d}):t().createElement(V,{className:n,delayMethod:r,delayTime:o,height:i,onVisible:this.onVisible,placeholder:a,style:l,threshold:u,width:d})}}])&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(a.prototype,n),a}(t().Component);X.propTypes={afterLoad:r.PropTypes.func,beforeLoad:r.PropTypes.func,useIntersectionObserver:r.PropTypes.bool,visibleByDefault:r.PropTypes.bool},X.defaultProps={afterLoad:function(){return{}},beforeLoad:function(){return{}},useIntersectionObserver:!0,visibleByDefault:!1};const U=X;function q(e){return(q="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var $=["afterLoad","beforeLoad","delayMethod","delayTime","effect","placeholder","placeholderSrc","scrollPosition","threshold","useIntersectionObserver","visibleByDefault","wrapperClassName","wrapperProps"];function K(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function J(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?K(Object(n),!0).forEach((function(t){Q(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):K(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Q(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Z(){return(Z=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function ee(e,t){return(ee=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function te(e,t){if(t&&("object"===q(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function ne(e){return(ne=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var re=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&ee(e,t)}(a,e);var n,r,o,i=(r=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=ne(r);if(o){var n=ne(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return te(this,e)});function a(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,a),(t=i.call(this,e)).state={loaded:!1},t}return(n=[{key:"onImageLoad",value:function(){var e=this;return this.state.loaded?null:function(){e.props.afterLoad(),e.setState({loaded:!0})}}},{key:"getImg",value:function(){var e=this.props,n=(e.afterLoad,e.beforeLoad,e.delayMethod,e.delayTime,e.effect,e.placeholder,e.placeholderSrc,e.scrollPosition,e.threshold,e.useIntersectionObserver,e.visibleByDefault,e.wrapperClassName,e.wrapperProps,function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,$));return t().createElement("img",Z({onLoad:this.onImageLoad()},n))}},{key:"getLazyLoadImage",value:function(){var e=this.props,n=e.beforeLoad,r=e.className,o=e.delayMethod,i=e.delayTime,a=e.height,c=e.placeholder,s=e.scrollPosition,l=e.style,u=e.threshold,p=e.useIntersectionObserver,d=e.visibleByDefault,f=e.width;return t().createElement(U,{beforeLoad:n,className:r,delayMethod:o,delayTime:i,height:a,placeholder:c,scrollPosition:s,style:l,threshold:u,useIntersectionObserver:p,visibleByDefault:d,width:f},this.getImg())}},{key:"getWrappedLazyLoadImage",value:function(e){var n=this.props,r=n.effect,o=n.height,i=n.placeholderSrc,a=n.width,c=n.wrapperClassName,s=n.wrapperProps,l=this.state.loaded,u=l?" lazy-load-image-loaded":"",p=l||!i?{}:{backgroundImage:"url(".concat(i,")"),backgroundSize:"100% 100%"};return t().createElement("span",Z({className:c+" lazy-load-image-background "+r+u,style:J(J({},p),{},{color:"transparent",display:"inline-block",height:o,width:a})},s),e)}},{key:"render",value:function(){var e=this.props,t=e.effect,n=e.placeholderSrc,r=e.visibleByDefault,o=e.wrapperClassName,i=e.wrapperProps,a=this.getLazyLoadImage();return(t||n)&&!r||o||i?this.getWrappedLazyLoadImage(a):a}}])&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(a.prototype,n),a}(t().Component);re.propTypes={afterLoad:r.PropTypes.func,beforeLoad:r.PropTypes.func,delayMethod:r.PropTypes.string,delayTime:r.PropTypes.number,effect:r.PropTypes.string,placeholderSrc:r.PropTypes.string,threshold:r.PropTypes.number,useIntersectionObserver:r.PropTypes.bool,visibleByDefault:r.PropTypes.bool,wrapperClassName:r.PropTypes.string,wrapperProps:r.PropTypes.object},re.defaultProps={afterLoad:function(){return{}},beforeLoad:function(){return{}},delayMethod:"throttle",delayTime:300,effect:"",placeholderSrc:null,threshold:100,useIntersectionObserver:!0,visibleByDefault:!1,wrapperClassName:""};const oe=re})(),e.exports=i})()},1405:function(e,t,n){"use strict";n.r(t),n.d(t,"default",(function(){return F}));var r=n(0),o=n(564),i=n(49),a=n(685),c=n(674),s=n(529),l=n(8),u=n(571),p=n(1227),d=n(2);const f=["ratio","disabledEffect","effect","sx"];function b(e){let{ratio:t,disabledEffect:n=!1,effect:r="blur",sx:o}=e,i=Object(u.a)(e,f);return t?Object(d.jsx)(s.a,{component:"span",sx:Object(l.a)({width:1,lineHeight:0,display:"block",overflow:"hidden",position:"relative",pt:h(t),"& .wrapper":{top:0,left:0,right:0,bottom:0,lineHeight:0,position:"absolute",backgroundSize:"cover !important"}},o),children:Object(d.jsx)(s.a,Object(l.a)({component:p.LazyLoadImage,wrapperClassName:"wrapper",effect:n?void 0:r,placeholderSrc:"https://zone-assets-api.vercel.app/assets/img_placeholder.svg",sx:{width:1,height:1,objectFit:"cover"}},i))}):Object(d.jsx)(s.a,{component:"span",sx:Object(l.a)({lineHeight:0,display:"block",overflow:"hidden","& .wrapper":{width:1,height:1,backgroundSize:"cover !important"}},o),children:Object(d.jsx)(s.a,Object(l.a)({component:p.LazyLoadImage,wrapperClassName:"wrapper",effect:n?void 0:r,placeholderSrc:"https://zone-assets-api.vercel.app/assets/img_placeholder.svg",sx:{width:1,height:1,objectFit:"cover"}},i))})}function h(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"1/1";return{"4/3":"calc(100% / 4 * 3)","3/4":"calc(100% / 3 * 4)","6/4":"calc(100% / 6 * 4)","4/6":"calc(100% / 4 * 6)","16/9":"calc(100% / 16 * 9)","9/16":"calc(100% / 9 * 16)","21/9":"calc(100% / 21 * 9)","9/21":"calc(100% / 9 * 21)","1/1":"100%"}[e]}const y=Object(i.a)(a.a)((e=>{let{theme:t}=e;return{position:"relative",display:"flex",height:"700px",alignItems:"center",justifyContent:"center",backgroundRepeat:"no-repeat",backgroundSize:"cover",backgroundPosition:"center",width:"100%",backgroundImage:"url(/images/bg.jpg)"}}));function m(){return Object(d.jsx)(y,{children:Object(d.jsxs)(a.a,{height:"100%",width:"100%",direction:{xs:"column",sm:"column",md:"row"},alignItems:"center",justifyContent:"center",sx:{backdropFilter:"blur(4px)"},marginTop:8,paddingX:{xs:2,sm:4,md:6},children:[Object(d.jsxs)(a.a,{padding:4,sx:{backdropFilter:"blur(2px)"},justifyContent:"flex-start",children:[Object(d.jsx)(c.a,{variant:"h5",sx:{mb:2},children:"\u041c\u0430\u0448\u0438\u043d\u0430\u0430 \u0443\u0442\u0441\u0430\u043d\u0434\u0430\u0430 \u0431\u0430\u0433\u0442\u0430\u0430"}),Object(d.jsx)(c.a,{color:"orange",variant:"h2",sx:{mb:2},children:"\u041c\u0430\u0448\u0438\u043d\u044b \u0445\u044f\u043d\u0430\u043b\u0442\u044b\u043d \u0441\u0438\u0441\u0442\u0435\u043c"}),Object(d.jsx)(c.a,{variant:"h5",sx:{mb:4},color:"text.secondary",children:"\u0410\u043b\u0441\u044b\u043d \u0430\u0441\u0430\u0430\u043b\u0442, \u0411\u0430\u0439\u0440\u0448\u0438\u043b \u0442\u043e\u0433\u0442\u043e\u043e\u0433\u0447, \u0445\u044f\u043d\u0430\u0445 \u0441\u0438\u0441\u0442\u0435\u043c, \u0434\u043e\u0445\u0438\u043e\u043b\u043e\u043b..."}),Object(d.jsx)(c.a,{variant:"h6",sx:{display:{xs:"block",md:"none"},mt:2},color:"text.secondary",children:"\u0443\u0442\u0430\u0441: 77372929"})]}),Object(d.jsx)(a.a,{padding:4,sx:{backdropFilter:"blur(2px)"},mb:8,children:Object(d.jsx)(s.a,{sx:{maxWidth:{xs:340,sm:400,md:500,lg:600}},children:Object(d.jsx)(b,{src:"/images/car-1.gif",alt:"Car Animation"})})})]})})}var j=n(732),v=n(673),O=n(606),g=n(5),x=n(71),w=n(240);const P=Object(i.a)(j.a)((e=>{let{theme:t}=e;return{transition:t.transitions.create(["height","background-color"],{easing:t.transitions.easing.easeInOut,duration:t.transitions.duration.shorter}),backdropFilter:"blur(60px)",width:"100%",position:"fixed",top:0,zIndex:999,left:0}}));function S(){const{t:e}=Object(o.a)(),{user:t}=Object(x.a)(),n=Object(g.l)();return Object(d.jsx)(P,{children:Object(d.jsx)(v.a,{children:Object(d.jsxs)(a.a,{direction:"row",justifyContent:"space-between",padding:2,children:[Object(d.jsx)(w.a,{sx:{height:{xs:36,sm:48,md:64},width:{xs:64,sm:84,md:124}}}),Object(d.jsxs)(a.a,{alignContent:"center",justifyContent:"center",sx:{display:{xs:"none",sm:"none",md:"flex"}},flexDirection:"row",gap:3,children:[Object(d.jsxs)(a.a,{direction:"row",alignItems:"center",gap:1,children:[Object(d.jsx)(O.a,{icon:"arcticons:callforwardingstatus",color:"orange",width:36}),Object(d.jsxs)(a.a,{children:[Object(d.jsx)(c.a,{variant:"subtitle2",children:e("\u0423\u0442\u0430\u0441")}),Object(d.jsx)(c.a,{variant:"h6",children:"77372929"})]})]}),Object(d.jsxs)(a.a,{direction:"row",alignItems:"center",gap:1,children:[Object(d.jsx)(O.a,{icon:"mdi:email-open-outline",color:"orange",width:36}),Object(d.jsxs)(a.a,{children:[Object(d.jsx)(c.a,{variant:"subtitle2",children:e("\u0415\u043c\u0430\u0439\u043b")}),Object(d.jsx)(c.a,{variant:"h6",children:"<EMAIL>"})]})]}),Object(d.jsxs)(a.a,{direction:"row",alignItems:"center",gap:1,children:[Object(d.jsx)(O.a,{icon:"ph:map-pin-line",color:"orange",width:36}),Object(d.jsxs)(a.a,{children:[Object(d.jsx)(c.a,{variant:"subtitle2",children:e("\u0425\u0430\u044f\u0433")}),Object(d.jsx)(c.a,{variant:"h6",children:"\u041a\u043e\u043c\u043f\u044c\u044e\u0442\u0435\u0440 \u041b\u0430\u043d\u0434 \u0431\u0430\u0440\u0443\u0443\u043d \u0445\u043e\u0439\u043d\u043e 26 \u0431\u0430\u0439\u0440 Electronic Parts"})]})]})]}),Object(d.jsx)(a.a,{alignItems:"center",justifyContent:"center",children:null==t?Object(d.jsx)(O.a,{onClick:()=>n("/auth/login"),icon:"iconoir:user-circle",width:36,color:"orange",cursor:"pointer"}):Object(d.jsx)(O.a,{onClick:()=>n("/auth/login"),icon:"ri:dashboard-3-line",width:36,color:"orange",cursor:"pointer"})})]})})})}var k=n(6),T=n.n(k),I=n(234);const R=["children","title","meta"],E=Object(r.forwardRef)(((e,t)=>{let{children:n,title:r="",meta:o}=e,i=Object(u.a)(e,R);return Object(d.jsxs)(d.Fragment,{children:[Object(d.jsxs)(I.a,{children:[Object(d.jsx)("title",{children:r}),o]}),Object(d.jsx)(s.a,Object(l.a)(Object(l.a)({ref:t},i),{},{children:n}))]})}));E.propTypes={children:T.a.node.isRequired,title:T.a.string,meta:T.a.node};var C=E,L=n(1142),M=n(237);const N=Object(i.a)(a.a)((e=>{let{theme:t}=e;return{position:"relative",display:"flex",alignItems:"center",justifyContent:"center",backgroundRepeat:"no-repeat",backgroundSize:"contain",backgroundPosition:"right",width:"100%"}}));function D(){const{t:e}=Object(o.a)();return Object(d.jsx)(N,{children:Object(d.jsxs)(a.a,{height:"100%",width:"100%",direction:{xs:"column",sm:"column",md:"row"},justifyContent:"center",sx:{backdropFilter:"blur(2px)"},paddingX:{xs:2,sm:4,md:6},children:[Object(d.jsxs)(a.a,{padding:4,sx:{backdropFilter:"blur(2px)"},children:[Object(d.jsx)(c.a,{variant:"h3",color:"orange",sx:{mb:2},children:e("\u0413\u0430\u0440 \u0443\u0442\u0430\u0441\u043d\u044b \u0430\u043f\u043f \u0442\u0430\u0442\u0430\u0445")}),Object(d.jsx)(s.a,{sx:{maxWidth:600,overflow:"hidden",borderRadius:"8px"},children:Object(d.jsx)(b,{src:"/images/download-app.gif",alt:""})})]}),Object(d.jsx)(a.a,{padding:4,mb:4,sx:{backdropFilter:"blur(2px)"},justifyContent:"flex-start",children:Object(d.jsxs)(a.a,{direction:{xs:"column",sm:"row"},justifyContent:"center",gap:2,sx:{mx:"auto",mb:3,mt:3},height:{xs:60,sm:70},children:[Object(d.jsx)(L.a,{href:"".concat(M.b,"direct-app-download/android-app.apk"),sx:{textDecoration:"none",color:"white"},children:Object(d.jsxs)(s.a,{sx:{cursor:"pointer"},flexDirection:"row",display:"flex",paddingY:2,paddingX:2,alignItems:"center",border:1,borderColor:"#38e8ff",borderRadius:2,gap:1,children:[Object(d.jsx)(O.a,{icon:"tabler:brand-android",width:48,color:"#38e8ff"}),Object(d.jsxs)(a.a,{px:2,children:[Object(d.jsx)(c.a,{variant:"caption",children:"Download APK"}),Object(d.jsx)(c.a,{variant:"h6",children:"Android"})]})]})}),Object(d.jsx)(L.a,{href:"https://apps.apple.com/mn/app/aslaa/id1671998041",sx:{textDecoration:"none",color:"white"},children:Object(d.jsxs)(s.a,{flexDirection:"row",sx:{cursor:"pointer"},display:"flex",paddingY:2,paddingX:2,alignItems:"center",border:1,borderColor:"#38e8ff",borderRadius:2,gap:1,children:[Object(d.jsx)(O.a,{icon:"ph:apple-logo-bold",width:48,color:"#38e8ff"}),Object(d.jsxs)(a.a,{px:2,children:[Object(d.jsx)(c.a,{variant:"caption",children:"Download from"}),Object(d.jsx)(c.a,{variant:"h6",children:"App Store"})]})]})})]})})]})})}var _=n(701);const B=Object(i.a)(a.a)((e=>{let{theme:t}=e;return{position:"relative",display:"flex",alignItems:"center",justifyContent:"center",borderRadius:16,backgroundColor:"#ff2f1f20",padding:8,minHeight:160,marginTop:"-80px",marginBottom:"80px"}}));function z(){return Object(d.jsxs)(a.a,{justifyContent:"center",alignItems:"center",children:[Object(d.jsxs)(B,{sx:{width:{xs:"90%",sm:"80%",md:"70%"}},children:[Object(d.jsxs)(_.a,{container:!0,spacing:3,children:[Object(d.jsxs)(_.a,{item:!0,xs:12,sm:3,children:[Object(d.jsxs)(a.a,{gap:1,justifyContent:"center",alignItems:"center",children:[Object(d.jsx)(O.a,{icon:"material-symbols:nest-remote-comfort-sensor-outline",width:64})," ",Object(d.jsx)(c.a,{variant:"h4",children:" \u0410\u043f\u043f \u0430\u0441\u0430\u0430\u043b\u0442 "})," "]})," "]})," ",Object(d.jsxs)(_.a,{item:!0,xs:12,sm:3,children:[Object(d.jsxs)(a.a,{gap:1,justifyContent:"center",alignItems:"center",children:[Object(d.jsx)(O.a,{icon:"mdi:message",width:64})," ",Object(d.jsx)(c.a,{variant:"h4",children:" \u041c\u0435\u0441\u0441\u0435\u0436 \u0430\u0441\u0430\u0430\u043b\u0442  "})," "]})," "]})," ",Object(d.jsxs)(_.a,{item:!0,xs:12,sm:3,children:[Object(d.jsxs)(a.a,{gap:1,justifyContent:"center",alignItems:"center",children:[Object(d.jsx)(O.a,{icon:"mdi:key-wireless",width:64})," ",Object(d.jsx)(c.a,{variant:"h4",children:" \u0422\u04af\u043b\u0445\u04af\u04af\u0440 \u0430\u0441\u0430\u0430\u043b\u0442 "})," "]})," "]})," ",Object(d.jsxs)(_.a,{item:!0,xs:12,sm:3,children:[Object(d.jsxs)(a.a,{gap:1,justifyContent:"center",alignItems:"center",children:[Object(d.jsx)(O.a,{icon:"mdi:chat",width:64})," ",Object(d.jsx)(c.a,{variant:"h4",children:" Messenger \u0430\u0441\u0430\u0430\u043b\u0442 "})," "]})," "]})," "]})," "]})," "]})}n(588);function W(){const{t:e}=Object(o.a)();return Object(d.jsx)(v.a,{sx:{mb:4},children:Object(d.jsx)(a.a,{children:Object(d.jsxs)(_.a,{container:!0,spacing:4,children:[Object(d.jsx)(_.a,{item:!0,xs:12,sm:12,md:6,children:Object(d.jsx)(a.a,{sx:{backgroundImage:"url(/images/map-bg.png)",backgroundRepeat:"no-repeat"},padding:6,borderRadius:2,children:Object(d.jsxs)(a.a,{gap:2,children:[Object(d.jsx)(c.a,{variant:"subtitle1",children:e("\u0411\u0438\u0434\u043d\u0438\u0439 \u0442\u0443\u0445\u0430\u0439")}),Object(d.jsx)(c.a,{variant:"h4",color:"orange",children:e("\u0411\u0438\u0434 \u04af\u0439\u043b\u0447\u0438\u043b\u0433\u044d\u044d\u043d\u0438\u0439 \u0441\u043e\u0451\u043b\u044b\u0433 \u0434\u0430\u0440\u0430\u0430\u0433\u0438\u0439\u043d \u0442\u04af\u0432\u0448\u0438\u043d\u0434....")}),Object(d.jsx)(c.a,{children:e("\u0411\u0438\u0434 \u0442\u0435\u0445\u043d\u0438\u043a \u0445\u0430\u043d\u0433\u0430\u043c\u0436 \u0431\u043e\u043b\u043e\u043d \u043f\u0440\u043e\u0433\u0440\u0430\u043c \u0445\u0430\u043d\u0433\u0430\u043c\u0436\u0438\u0439\u043d \u0447\u0438\u0433\u043b\u044d\u043b\u044d\u044d\u0440 \u04af\u0439\u043b \u0430\u0436\u0438\u043b\u043b\u0430\u0433\u0430\u0430 \u044f\u0432\u0443\u0443\u043b\u0434\u0430\u0433 \u043c\u043e\u043d\u0433\u043e\u043b\u044b\u043d \u0446\u04e9\u04e9\u043d \u043a\u043e\u043c\u043f\u0430\u043d\u0438\u0443\u0434\u044b\u043d \u043d\u044d\u0433 \u0431\u0438\u043b\u044d\u044d. \u0411\u0438\u0434 \u04af\u0440\u0433\u044d\u043b\u0436 \u0448\u0438\u043d\u0438\u0439\u0433 \u044d\u0440\u044d\u043b\u0445\u0438\u0439\u043b\u0436 \u0448\u0438\u043d\u0438\u0439\u0433 \u0441\u0430\u043d\u0430\u0430\u0447\u0438\u043b\u0430\u043d \u0445\u044d\u0440\u044d\u0433\u043b\u044d\u0433\u0447\u0438\u0434\u0438\u0439\u043d\u0445\u044d\u044d \u0442\u0430\u0432 \u0442\u0443\u0445, \u0446\u0430\u0433 \u0437\u0430\u0432\u044b\u0433 \u0445\u04e9\u043d\u0433\u04e9\u0432\u0447\u043b\u04e9\u0445 \u0431\u04af\u0442\u044d\u044d\u0433\u0434\u0445\u04af\u04af\u043d\u0438\u0439\u0433 \u0433\u0430\u0440\u0433\u0430\u0445\u044b\u043d \u0442\u04e9\u043b\u04e9\u04e9 \u0430\u0436\u0438\u043b\u0434\u0430\u0433")})]})})}),Object(d.jsx)(_.a,{item:!0,xs:12,sm:12,md:6,children:Object(d.jsx)(a.a,{sx:{backgroundColor:"orange"},padding:6,borderRadius:2,children:Object(d.jsxs)(a.a,{gap:2,children:[Object(d.jsx)(c.a,{variant:"subtitle1",children:e("\u042f\u0430\u0433\u0430\u0430\u0434 \u0431\u0438\u0434 \u0433\u044d\u0436?")}),Object(d.jsx)(c.a,{children:e("\u0411\u0438\u0434 \u04af\u0439\u043b\u0447\u0438\u043b\u0433\u044d\u044d\u0433\u044d\u044d \u0442\u0430\u0441\u0440\u0430\u043b\u0442\u0433\u04af\u0439 \u0441\u0430\u0439\u0436\u0440\u0443\u0443\u043b\u0434\u0430\u0433")}),Object(d.jsxs)(a.a,{gap:2,children:[Object(d.jsxs)(a.a,{direction:"row",gap:4,alignItems:"center",children:[Object(d.jsx)(O.a,{icon:"mdi:diamond-stone",width:64}),Object(d.jsxs)(a.a,{gap:1,children:[Object(d.jsx)(c.a,{variant:"h6",children:e("\u041d\u0430\u0439\u0434\u0432\u0430\u0440\u0442\u0430\u0439 \u0442\u04af\u043d\u0448")}),Object(d.jsx)(c.a,{children:e("\u0411\u0438\u0434 \u04af\u0440\u0433\u044d\u0436 \u0448\u0438\u043d\u0438\u0439\u0433 \u0441\u0430\u043d\u0430\u0430\u0447\u0438\u043b\u0434\u0430\u0433")})]})]}),Object(d.jsxs)(a.a,{direction:"row",gap:4,alignItems:"center",children:[Object(d.jsx)(O.a,{icon:"material-symbols:directions-car-outline-rounded",width:64}),Object(d.jsxs)(a.a,{gap:1,children:[Object(d.jsx)(c.a,{variant:"h6",children:e("\u0428\u0443\u0443\u0440\u0445\u0430\u0439 \u04af\u0439\u043b\u0447\u0438\u043b\u0433\u044d\u044d")}),Object(d.jsx)(c.a,{children:e("\u0427\u0430\u043d\u0430\u0440\u044b\u043d \u0431\u0430\u0442\u0430\u043b\u0433\u0430\u0430 \u04e9\u0433\u0434\u04e9\u0433")})]})]}),Object(d.jsxs)(a.a,{direction:"row",gap:4,alignItems:"center",children:[Object(d.jsx)(O.a,{icon:"ri:dashboard-3-line",width:64}),Object(d.jsxs)(a.a,{gap:1,children:[Object(d.jsx)(c.a,{variant:"h6",children:e("\u0426\u0430\u0433 \u0431\u043e\u043b \u0430\u043b\u0442")}),Object(d.jsx)(c.a,{children:e("\u0425\u044f\u043c\u0434 \u04af\u043d\u044d \u044d\u0434\u0438\u0439\u043d \u0437\u0430\u0441\u0433\u0438\u0439\u043d \u0445\u044d\u043c\u043d\u044d\u043b\u0442")})]})]})]})]})})})]})})})}var V=n(246);function F(){const{t:e}=Object(o.a)();return Object(r.useEffect)((()=>{(async()=>{try{if("granted"===await Notification.requestPermission()){console.log("Notification permission granted.");const e=await Object(V.a)(V.b,{vapidKey:"BDMievQt-9l21wJxGBQ-9Qamb6igxvWMnKNV-26s5Y-BV-kUoM7RJs_7DWelbZ0qU8e5P5Lct0vWQvZKr8mhYKo"});console.log("FCM Token:",e)}else console.log("Notification permission denied.")}catch(e){console.error("Error getting permission or token:",e)}})()}),[]),Object(d.jsxs)(C,{title:e("aslaa"),children:[Object(d.jsx)(S,{}),Object(d.jsx)(m,{}),Object(d.jsx)(z,{}),Object(d.jsx)(D,{}),Object(d.jsx)(W,{})]})}},587:function(e,t,n){"use strict";var r=n(8),o=n(571),i=n(6),a=n.n(i),c=n(721),s=n(0),l=n(1431),u=n(529),p=n(2);const d=["children","size"],f=Object(s.forwardRef)(((e,t)=>{let{children:n,size:i="medium"}=e,a=Object(o.a)(e,d);return Object(p.jsx)(m,{size:i,children:Object(p.jsx)(l.a,Object(r.a)(Object(r.a)({size:i,ref:t},a),{},{children:n}))})}));f.propTypes={children:a.a.node.isRequired,color:a.a.oneOf(["inherit","default","primary","secondary","info","success","warning","error"]),size:a.a.oneOf(["small","medium","large"])},t.a=f;const b={hover:{scale:1.1},tap:{scale:.95}},h={hover:{scale:1.09},tap:{scale:.97}},y={hover:{scale:1.08},tap:{scale:.99}};function m(e){let{size:t,children:n}=e;const r="small"===t,o="large"===t;return Object(p.jsx)(u.a,{component:c.a.div,whileTap:"tap",whileHover:"hover",variants:r&&b||o&&y||h,sx:{display:"inline-flex"},children:n})}},588:function(e,t,n){"use strict";n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return d.a})),n.d(t,"b",(function(){return b}));const r=e=>({duration:(null===e||void 0===e?void 0:e.durationIn)||.64,ease:(null===e||void 0===e?void 0:e.easeIn)||[.43,.13,.23,.96]}),o=e=>({duration:(null===e||void 0===e?void 0:e.durationOut)||.48,ease:(null===e||void 0===e?void 0:e.easeOut)||[.43,.13,.23,.96]});var i=n(8);const a=e=>{const t=null===e||void 0===e?void 0:e.durationIn,n=null===e||void 0===e?void 0:e.durationOut,a=null===e||void 0===e?void 0:e.easeIn,c=null===e||void 0===e?void 0:e.easeOut;return{in:{initial:{},animate:{scale:[.3,1.1,.9,1.03,.97,1],opacity:[0,1,1,1,1,1],transition:r({durationIn:t,easeIn:a})},exit:{scale:[.9,1.1,.3],opacity:[1,1,0]}},inUp:{initial:{},animate:{y:[720,-24,12,-4,0],scaleY:[4,.9,.95,.985,1],opacity:[0,1,1,1,1],transition:Object(i.a)({},r({durationIn:t,easeIn:a}))},exit:{y:[12,-24,720],scaleY:[.985,.9,3],opacity:[1,1,0],transition:o({durationOut:n,easeOut:c})}},inDown:{initial:{},animate:{y:[-720,24,-12,4,0],scaleY:[4,.9,.95,.985,1],opacity:[0,1,1,1,1],transition:r({durationIn:t,easeIn:a})},exit:{y:[-12,24,-720],scaleY:[.985,.9,3],opacity:[1,1,0],transition:o({durationOut:n,easeOut:c})}},inLeft:{initial:{},animate:{x:[-720,24,-12,4,0],scaleX:[3,1,.98,.995,1],opacity:[0,1,1,1,1],transition:r({durationIn:t,easeIn:a})},exit:{x:[0,24,-720],scaleX:[1,.9,2],opacity:[1,1,0],transition:o({durationOut:n,easeOut:c})}},inRight:{initial:{},animate:{x:[720,-24,12,-4,0],scaleX:[3,1,.98,.995,1],opacity:[0,1,1,1,1],transition:r({durationIn:t,easeIn:a})},exit:{x:[0,-24,720],scaleX:[1,.9,2],opacity:[1,1,0],transition:o({durationOut:n,easeOut:c})}},out:{animate:{scale:[.9,1.1,.3],opacity:[1,1,0]}},outUp:{animate:{y:[-12,24,-720],scaleY:[.985,.9,3],opacity:[1,1,0]}},outDown:{animate:{y:[12,-24,720],scaleY:[.985,.9,3],opacity:[1,1,0]}},outLeft:{animate:{x:[0,24,-720],scaleX:[1,.9,2],opacity:[1,1,0]}},outRight:{animate:{x:[0,-24,720],scaleX:[1,.9,2],opacity:[1,1,0]}}}},c=e=>({animate:{transition:{staggerChildren:(null===e||void 0===e?void 0:e.staggerIn)||.05,delayChildren:(null===e||void 0===e?void 0:e.staggerIn)||.05}},exit:{transition:{staggerChildren:(null===e||void 0===e?void 0:e.staggerIn)||.05,staggerDirection:-1}}});var s=n(571),l=(n(726),n(721)),u=(n(704),n(529)),p=(n(1418),n(2));n(0),n(124),n(729);var d=n(587);n(728),n(627);const f=["animate","action","children"];function b(e){let{animate:t,action:n=!1,children:r}=e,o=Object(s.a)(e,f);return n?Object(p.jsx)(u.a,Object(i.a)(Object(i.a)({component:l.a.div,initial:!1,animate:t?"animate":"exit",variants:c()},o),{},{children:r})):Object(p.jsx)(u.a,Object(i.a)(Object(i.a)({component:l.a.div,initial:"initial",animate:"animate",exit:"exit",variants:c()},o),{},{children:r}))}n(722)},685:function(e,t,n){"use strict";var r=n(11),o=n(3),i=n(0),a=n(25),c=n(7),s=n(562),l=n(179),u=n(49),p=n(69),d=n(2);const f=["component","direction","spacing","divider","children"];function b(e,t){const n=i.Children.toArray(e).filter(Boolean);return n.reduce(((e,r,o)=>(e.push(r),o<n.length-1&&e.push(i.cloneElement(t,{key:"separator-".concat(o)})),e)),[])}const h=Object(u.a)("div",{name:"MuiStack",slot:"Root",overridesResolver:(e,t)=>[t.root]})((e=>{let{ownerState:t,theme:n}=e,r=Object(o.a)({display:"flex",flexDirection:"column"},Object(a.b)({theme:n},Object(a.e)({values:t.direction,breakpoints:n.breakpoints.values}),(e=>({flexDirection:e}))));if(t.spacing){const e=Object(c.a)(n),o=Object.keys(n.breakpoints.values).reduce(((e,n)=>(("object"===typeof t.spacing&&null!=t.spacing[n]||"object"===typeof t.direction&&null!=t.direction[n])&&(e[n]=!0),e)),{}),i=Object(a.e)({values:t.direction,base:o}),s=Object(a.e)({values:t.spacing,base:o});"object"===typeof i&&Object.keys(i).forEach(((e,t,n)=>{if(!i[e]){const r=t>0?i[n[t-1]]:"column";i[e]=r}}));const u=(n,r)=>{return{"& > :not(style) + :not(style)":{margin:0,["margin".concat((o=r?i[r]:t.direction,{row:"Left","row-reverse":"Right",column:"Top","column-reverse":"Bottom"}[o]))]:Object(c.c)(e,n)}};var o};r=Object(l.a)(r,Object(a.b)({theme:n},s,u))}return r=Object(a.c)(n.breakpoints,r),r})),y=i.forwardRef((function(e,t){const n=Object(p.a)({props:e,name:"MuiStack"}),i=Object(s.a)(n),{component:a="div",direction:c="column",spacing:l=0,divider:u,children:y}=i,m=Object(r.a)(i,f),j={direction:c,spacing:l};return Object(d.jsx)(h,Object(o.a)({as:a,ownerState:j,ref:t},m,{children:u?b(y,u):y}))}));t.a=y},701:function(e,t,n){"use strict";var r=n(11),o=n(3),i=n(0),a=n(42),c=n(25),s=n(562),l=n(558),u=n(49),p=n(69),d=n(124);var f=i.createContext(),b=n(559),h=n(525);function y(e){return Object(h.a)("MuiGrid",e)}const m=["auto",!0,1,2,3,4,5,6,7,8,9,10,11,12];var j=Object(b.a)("MuiGrid",["root","container","item","zeroMinWidth",...[0,1,2,3,4,5,6,7,8,9,10].map((e=>"spacing-xs-".concat(e))),...["column-reverse","column","row-reverse","row"].map((e=>"direction-xs-".concat(e))),...["nowrap","wrap-reverse","wrap"].map((e=>"wrap-xs-".concat(e))),...m.map((e=>"grid-xs-".concat(e))),...m.map((e=>"grid-sm-".concat(e))),...m.map((e=>"grid-md-".concat(e))),...m.map((e=>"grid-lg-".concat(e))),...m.map((e=>"grid-xl-".concat(e)))]),v=n(2);const O=["className","columns","columnSpacing","component","container","direction","item","rowSpacing","spacing","wrap","zeroMinWidth"];function g(e){const t=parseFloat(e);return"".concat(t).concat(String(e).replace(String(t),"")||"px")}function x(e){let{breakpoints:t,values:n}=e,r="";Object.keys(n).forEach((e=>{""===r&&0!==n[e]&&(r=e)}));const o=Object.keys(t).sort(((e,n)=>t[e]-t[n]));return o.slice(0,o.indexOf(r))}const w=Object(u.a)("div",{name:"MuiGrid",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e,{container:r,direction:o,item:i,spacing:a,wrap:c,zeroMinWidth:s,breakpoints:l}=n;let u=[];r&&(u=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!e||e<=0)return[];if("string"===typeof e&&!Number.isNaN(Number(e))||"number"===typeof e)return[n["spacing-xs-".concat(String(e))]];const r=[];return t.forEach((t=>{const o=e[t];Number(o)>0&&r.push(n["spacing-".concat(t,"-").concat(String(o))])})),r}(a,l,t));const p=[];return l.forEach((e=>{const r=n[e];r&&p.push(t["grid-".concat(e,"-").concat(String(r))])})),[t.root,r&&t.container,i&&t.item,s&&t.zeroMinWidth,...u,"row"!==o&&t["direction-xs-".concat(String(o))],"wrap"!==c&&t["wrap-xs-".concat(String(c))],...p]}})((e=>{let{ownerState:t}=e;return Object(o.a)({boxSizing:"border-box"},t.container&&{display:"flex",flexWrap:"wrap",width:"100%"},t.item&&{margin:0},t.zeroMinWidth&&{minWidth:0},"wrap"!==t.wrap&&{flexWrap:t.wrap})}),(function(e){let{theme:t,ownerState:n}=e;const r=Object(c.e)({values:n.direction,breakpoints:t.breakpoints.values});return Object(c.b)({theme:t},r,(e=>{const t={flexDirection:e};return 0===e.indexOf("column")&&(t["& > .".concat(j.item)]={maxWidth:"none"}),t}))}),(function(e){let{theme:t,ownerState:n}=e;const{container:r,rowSpacing:o}=n;let i={};if(r&&0!==o){const e=Object(c.e)({values:o,breakpoints:t.breakpoints.values});let n;"object"===typeof e&&(n=x({breakpoints:t.breakpoints.values,values:e})),i=Object(c.b)({theme:t},e,((e,r)=>{var o;const i=t.spacing(e);return"0px"!==i?{marginTop:"-".concat(g(i)),["& > .".concat(j.item)]:{paddingTop:g(i)}}:null!=(o=n)&&o.includes(r)?{}:{marginTop:0,["& > .".concat(j.item)]:{paddingTop:0}}}))}return i}),(function(e){let{theme:t,ownerState:n}=e;const{container:r,columnSpacing:o}=n;let i={};if(r&&0!==o){const e=Object(c.e)({values:o,breakpoints:t.breakpoints.values});let n;"object"===typeof e&&(n=x({breakpoints:t.breakpoints.values,values:e})),i=Object(c.b)({theme:t},e,((e,r)=>{var o;const i=t.spacing(e);return"0px"!==i?{width:"calc(100% + ".concat(g(i),")"),marginLeft:"-".concat(g(i)),["& > .".concat(j.item)]:{paddingLeft:g(i)}}:null!=(o=n)&&o.includes(r)?{}:{width:"100%",marginLeft:0,["& > .".concat(j.item)]:{paddingLeft:0}}}))}return i}),(function(e){let t,{theme:n,ownerState:r}=e;return n.breakpoints.keys.reduce(((e,i)=>{let a={};if(r[i]&&(t=r[i]),!t)return e;if(!0===t)a={flexBasis:0,flexGrow:1,maxWidth:"100%"};else if("auto"===t)a={flexBasis:"auto",flexGrow:0,flexShrink:0,maxWidth:"none",width:"auto"};else{const s=Object(c.e)({values:r.columns,breakpoints:n.breakpoints.values}),l="object"===typeof s?s[i]:s;if(void 0===l||null===l)return e;const u="".concat(Math.round(t/l*1e8)/1e6,"%");let p={};if(r.container&&r.item&&0!==r.columnSpacing){const e=n.spacing(r.columnSpacing);if("0px"!==e){const t="calc(".concat(u," + ").concat(g(e),")");p={flexBasis:t,maxWidth:t}}}a=Object(o.a)({flexBasis:u,flexGrow:0,maxWidth:u},p)}return 0===n.breakpoints.values[i]?Object.assign(e,a):e[n.breakpoints.up(i)]=a,e}),{})}));const P=e=>{const{classes:t,container:n,direction:r,item:o,spacing:i,wrap:a,zeroMinWidth:c,breakpoints:s}=e;let u=[];n&&(u=function(e,t){if(!e||e<=0)return[];if("string"===typeof e&&!Number.isNaN(Number(e))||"number"===typeof e)return["spacing-xs-".concat(String(e))];const n=[];return t.forEach((t=>{const r=e[t];if(Number(r)>0){const e="spacing-".concat(t,"-").concat(String(r));n.push(e)}})),n}(i,s));const p=[];s.forEach((t=>{const n=e[t];n&&p.push("grid-".concat(t,"-").concat(String(n)))}));const d={root:["root",n&&"container",o&&"item",c&&"zeroMinWidth",...u,"row"!==r&&"direction-xs-".concat(String(r)),"wrap"!==a&&"wrap-xs-".concat(String(a)),...p]};return Object(l.a)(d,y,t)},S=i.forwardRef((function(e,t){const n=Object(p.a)({props:e,name:"MuiGrid"}),{breakpoints:c}=Object(d.a)(),l=Object(s.a)(n),{className:u,columns:b,columnSpacing:h,component:y="div",container:m=!1,direction:j="row",item:g=!1,rowSpacing:x,spacing:S=0,wrap:k="wrap",zeroMinWidth:T=!1}=l,I=Object(r.a)(l,O),R=x||S,E=h||S,C=i.useContext(f),L=m?b||12:C,M={},N=Object(o.a)({},I);c.keys.forEach((e=>{null!=I[e]&&(M[e]=I[e],delete N[e])}));const D=Object(o.a)({},l,{columns:L,container:m,direction:j,item:g,rowSpacing:R,columnSpacing:E,wrap:k,zeroMinWidth:T,spacing:S},M,{breakpoints:c.keys}),_=P(D);return Object(v.jsx)(f.Provider,{value:L,children:Object(v.jsx)(w,Object(o.a)({ownerState:D,className:Object(a.a)(_.root,u),as:y,ref:t},N))})}));t.a=S},732:function(e,t,n){"use strict";var r=n(11),o=n(3),i=n(0),a=n(42),c=n(558),s=n(69),l=n(49),u=n(559),p=n(525);function d(e){return Object(p.a)("MuiToolbar",e)}Object(u.a)("MuiToolbar",["root","gutters","regular","dense"]);var f=n(2);const b=["className","component","disableGutters","variant"],h=Object(l.a)("div",{name:"MuiToolbar",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,!n.disableGutters&&t.gutters,t[n.variant]]}})((e=>{let{theme:t,ownerState:n}=e;return Object(o.a)({position:"relative",display:"flex",alignItems:"center"},!n.disableGutters&&{paddingLeft:t.spacing(2),paddingRight:t.spacing(2),[t.breakpoints.up("sm")]:{paddingLeft:t.spacing(3),paddingRight:t.spacing(3)}},"dense"===n.variant&&{minHeight:48})}),(e=>{let{theme:t,ownerState:n}=e;return"regular"===n.variant&&t.mixins.toolbar})),y=i.forwardRef((function(e,t){const n=Object(s.a)({props:e,name:"MuiToolbar"}),{className:i,component:l="div",disableGutters:u=!1,variant:p="regular"}=n,y=Object(r.a)(n,b),m=Object(o.a)({},n,{component:l,disableGutters:u,variant:p}),j=(e=>{const{classes:t,disableGutters:n,variant:r}=e,o={root:["root",!n&&"gutters",r]};return Object(c.a)(o,d,t)})(m);return Object(f.jsx)(h,Object(o.a)({as:l,className:Object(a.a)(j.root,i),ref:t,ownerState:m},y))}));t.a=y}}]);
//# sourceMappingURL=36.5143fb86.chunk.js.map