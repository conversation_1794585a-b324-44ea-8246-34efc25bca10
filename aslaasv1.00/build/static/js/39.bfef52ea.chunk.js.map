{"version": 3, "sources": ["sections/auth/VerifyCodeForm.js", "pages/auth/VerifyCode.js", "components/Iconify.js", "components/Page.js", "components/CarFront.js"], "names": ["VerifyCodeForm", "navigate", "useNavigate", "otpVerify", "useAuth", "enqueueSnackbar", "useSnackbar", "VerifyCodeSchema", "<PERSON><PERSON>", "shape", "code1", "required", "code2", "code3", "code4", "code5", "code6", "watch", "control", "setValue", "handleSubmit", "formState", "isSubmitting", "<PERSON><PERSON><PERSON><PERSON>", "useForm", "mode", "resolver", "yupResolver", "defaultValues", "values", "useEffect", "document", "addEventListener", "handlePasteClipboard", "event", "_event$clipboardData", "data", "clipboardData", "getData", "split", "for<PERSON>ach", "call", "querySelectorAll", "node", "index", "value", "fieldIndex", "concat", "_jsx", "_Fragment", "children", "_jsxs", "onSubmit", "async", "afterLogin", "result", "success", "variant", "Object", "join", "error", "console", "<PERSON><PERSON>", "direction", "spacing", "mb", "justifyContent", "keys", "map", "name", "Controller", "render", "_ref", "field", "InputBase", "_objectSpread", "id", "autoFocus", "placeholder", "onChange", "handleChangeWithNextField", "handleChange", "max<PERSON><PERSON><PERSON>", "target", "replace", "fieldIntIndex", "Number", "length", "nextfield", "querySelector", "focus", "inputProps", "sx", "py", "textAlign", "width", "xs", "sm", "height", "fontSize", "bgcolor", "borderRadius", "LoadingButton", "size", "type", "loading", "disabled", "border", "borderColor", "RootStyle", "styled", "theme", "display", "alignItems", "padding", "VerifyCode", "Page", "title", "LogoOnlyLayout", "Container", "Box", "max<PERSON><PERSON><PERSON>", "mx", "<PERSON><PERSON>", "component", "RouterLink", "to", "startIcon", "Iconify", "icon", "color", "CarFront", "Typography", "paragraph", "mt", "Link", "underline", "cursor", "onClick", "other", "_objectWithoutProperties", "_excluded", "Icon", "forwardRef", "ref", "meta", "<PERSON><PERSON><PERSON>", "propTypes", "PropTypes", "isRequired", "string", "disabledLink", "useTheme", "PRIMARY_MAIN", "undefined", "palette", "grey", "svg", "version", "xmlns", "viewBox", "preserveAspectRatio", "transform", "fill", "stroke", "d"], "mappings": "oWAce,SAASA,IACtB,MAAMC,EAAWC,eACX,UAAEC,GAAcC,eAChB,gBAAEC,GAAoBC,cAEtBC,EAAmBC,MAAaC,MAAM,CAC1CC,MAAOF,MAAaG,SAAS,oBAC7BC,MAAOJ,MAAaG,SAAS,oBAC7BE,MAAOL,MAAaG,SAAS,oBAC7BG,MAAON,MAAaG,SAAS,oBAC7BI,MAAOP,MAAaG,SAAS,oBAC7BK,MAAOR,MAAaG,SAAS,uBAYzB,MACJM,EAAK,QACLC,EAAO,SACPC,EAAQ,aACRC,EACAC,WAAW,aAAEC,EAAY,QAAEC,IACzBC,YAAQ,CACVC,KAAM,SACNC,SAAUC,YAAYpB,GACtBqB,cAlBoB,CACpBlB,MAAO,GACPE,MAAO,GACPC,MAAO,GACPC,MAAO,GACPC,MAAO,GACPC,MAAO,MAeHa,EAASZ,IAEfa,qBAAU,KACRC,SAASC,iBAAiB,QAASC,EAAqB,GAEvD,IAEH,MAkBMA,EAAwBC,IAAW,IAADC,EACtC,IAAIC,GAAY,OAALF,QAAK,IAALA,GAAoB,QAAfC,EAALD,EAAOG,qBAAa,IAAAF,OAAf,EAALA,EAAsBG,QAAQ,UAAW,GAEpDF,EAAOA,EAAKG,MAAM,IAElB,GAAGC,QAAQC,KAAKV,SAASW,iBAAiB,gBAAgB,CAACC,EAAMC,KAC/DD,EAAKE,MAAQT,EAAKQ,GAClB,MAAME,EAAU,OAAAC,OAAUH,EAAQ,GAClCzB,EAAS2B,EAAYV,EAAKQ,GAAO,GACjC,EAsBJ,OACEI,cAAAC,WAAA,CAAAC,SACAC,eAAA,QAAMC,SAAUhC,GAnDDiC,UACf,IACE,MAAMC,EAAcC,IACfA,EAAOC,SACRnD,EAAgB,gCAAiC,CAACoD,QAAQ,YAC1DxD,EAAS,MAGTI,EAAgB,qCAAsC,CAACoD,QAAQ,SACjE,EAEDtD,EAAUuD,OAAO7B,OAAOO,GAAMuB,KAAK,IAAIL,EAI1C,CAFE,MAAOM,GACPC,QAAQD,MAAMA,EAChB,KAoCuCV,SAAA,CACrCF,cAACc,IAAK,CAACC,UAAU,MAAMC,QAAS,EAAGC,GAAI,EAAGC,eAAe,SAAQhB,SAC9DQ,OAAOS,KAAKtC,GAAQuC,KAAI,CAACC,EAAMzB,IAC9BI,cAACsB,IAAU,CAETD,KAAI,OAAAtB,OAASH,EAAQ,GACrB1B,QAASA,EACTqD,OAAQC,IAAA,IAAC,MAAEC,GAAOD,EAAA,OAChBxB,cAAC0B,IAASC,wBAAA,GACJF,GAAK,IACTG,GAAG,aACHC,UAAqB,IAAVjC,EACXkC,YAAY,IACZC,SAAW7C,GAlCS8C,EAAC9C,EAAO+C,KACxC,MAAM,UAAEC,EAAS,MAAErC,EAAK,KAAEwB,GAASnC,EAAMiD,OACnCrC,EAAauB,EAAKe,QAAQ,OAAQ,IAElCC,EAAgBC,OAAOxC,GAE7B,GAAID,EAAM0C,QAAUL,GACdG,EAAgB,EAAG,CACrB,MAAMG,EAAYzD,SAAS0D,cAAc,kBAAD1C,OAAmBsC,EAAgB,EAAC,MAE1D,OAAdG,GACFA,EAAUE,OAEd,CAGFT,EAAa/C,EAAM,EAkBc8C,CAA0B9C,EAAOuC,EAAMM,UAC5DY,WAAY,CACVT,UAAW,EACXU,GAAI,CACFC,GAAI,GACJC,UAAW,SACXC,MAAO,CAAEC,GAAI,GAAIC,GAAI,IACrBC,OAAQ,CAAEF,GAAI,GAAIC,GAAI,MAG1BL,GAAI,CAAEO,SAAU,SAAUC,QAAS,aAAcC,aAAc,KAC/D,GApBChC,OA0BXrB,cAACsD,IAAa,CACZC,KAAK,QACLC,KAAK,SACL/C,QAAQ,YACRgD,QAASnF,EACToF,UAAWnF,EACXqE,GAAI,CAACQ,QAAQ,aAAaO,OAAO,YAAYC,YAAY,cAAc1D,SACxE,aAOP,CCvIA,MAAM2D,EAAYC,YAAO,MAAPA,EAActC,IAAA,IAAC,MAAEuC,GAAOvC,EAAA,MAAM,CAC9CwC,QAAS,OACTC,WAAY,SACZC,QAASH,EAAM/C,QAAQ,EAAG,GAC1BkC,OAAO,QACR,IAIc,SAASiB,IACtB,OACEnE,cAACoE,IAAI,CAACC,MAAM,SAASzB,GAAI,CAAEM,OAAQ,GAAIhD,SACrCC,eAAC0D,EAAS,CAAA3D,SAAA,CACRF,cAACsE,IAAc,IAEfnE,eAACoE,IAAS,CAAArE,SAAA,CACRC,eAACqE,IAAG,CAAC5B,GAAI,CAAE6B,SAAU,IAAKC,GAAI,OAAQ5B,UAAW,UAAW5C,SAAA,CAC1DF,cAAC2E,IAAM,CACLpB,KAAK,QACLqB,UAAWC,IACXC,GAAI,cACJC,UAAW/E,cAACgF,IAAO,CAACC,KAAM,0BAA2BlC,MAAO,GAAIG,OAAQ,KACxEN,GAAI,CAACsC,MAAM,gBAAiBhF,SAC7B,SAGDF,cAACwE,IAAG,CAACzB,MAAO,MAAOH,GAAI,CAAE8B,GAAI,OAAQzD,GAAI,GAAGf,SAC3CF,cAACmF,UAAQ,MAEVnF,cAACoF,IAAU,CAAC3E,QAAQ,KAAK4E,WAAS,EAAAnF,SAAE,sBAGpCF,cAACoF,IAAU,CAAEC,WAAS,EAAAnF,SAAC,gEAGvBF,cAACwE,IAAG,CAAC5B,GAAI,CAAE0C,GAAI,EAAGrE,GAAI,GAAIf,SACxBF,cAAChD,EAAc,MAEjBmD,eAACiF,IAAU,CAAC3E,QAAQ,QAAOP,SAAA,CAAC,yCAE1BF,cAACuF,IAAI,CAAC9E,QAAQ,YAAY+E,UAAU,OAAO5C,GAAI,CAAC6C,OAAO,UAAUP,MAAM,kBAAmBQ,QAASA,OAAUxF,SAAC,sBAKlHF,cAAA,OAAK4B,GAAK,+BAKpB,C,wICpDe,SAASoD,EAAOxD,GAA0B,IAAzB,KAAEyD,EAAI,GAAErC,GAAcpB,EAAPmE,EAAKC,YAAApE,EAAAqE,GAClD,OAAO7F,cAACwE,IAAG7C,YAAA,CAACiD,UAAWkB,IAAMb,KAAMA,EAAMrC,GAAEjB,YAAA,GAAOiB,IAAU+C,GAC9D,C,oJCRMvB,EAAO2B,sBAAW,CAAAvE,EAA2CwE,KAAG,IAA7C,SAAE9F,EAAQ,MAAEmE,EAAQ,GAAE,KAAE4B,GAAgBzE,EAAPmE,EAAKC,YAAApE,EAAAqE,GAAA,OAC7D1F,eAAAF,WAAA,CAAAC,SAAA,CACEC,eAAC+F,IAAM,CAAAhG,SAAA,CACLF,cAAA,SAAAE,SAAQmE,IACP4B,KAGHjG,cAACwE,IAAG7C,wBAAA,CAACqE,IAAKA,GAASL,GAAK,IAAAzF,SACtBF,cAACuE,IAAS,CAAArE,SACPA,SAIJ,IAGLkE,EAAK+B,UAAY,CACfjG,SAAUkG,IAAUzG,KAAK0G,WACzBhC,MAAO+B,IAAUE,OACjBL,KAAMG,IAAUzG,MAGHyE,K,8HCdA,SAASe,EAAQ3D,GAAsC,IAArC,aAAE+E,GAAe,EAAK,GAAE3D,EAAE,MAACsC,GAAO1D,EACjE,MAAMuC,EAAQyC,cACRC,OAAuBC,IAARxB,EAAkBA,EAAMnB,EAAM4C,QAAQC,KAAK,OAG1DC,EACJ7G,cAACwE,IAAG,CAAC5B,GAAEjB,YAAA,CAAIoB,MAAO,UAAWG,OAAQ,WAAcN,GAAK1C,SAGtDF,cAAA,OAAK8G,QAAQ,MAAMC,MAAM,6BACvBhE,MAAM,OAAOG,OAAO,OAAO8D,QAAQ,4BACnCC,oBAAoB,gBAAe/G,SAEnCF,cAAA,KAAGkH,UAAU,2DACXC,KAAMV,EAAcW,OAAO,OAAMlH,SACjCF,cAAA,QAAMqH,EAAE,slDA4BhB,OAAId,EACKvG,cAAAC,WAAA,CAAAC,SAAG2G,IAGL7G,cAAC6E,IAAU,CAACC,GAAG,IAAG5E,SAAE2G,GAC7B,C", "file": "static/js/39.bfef52ea.chunk.js", "sourcesContent": ["import * as Yup from 'yup';\nimport { useSnackbar } from 'notistack';\nimport { useNavigate } from 'react-router-dom';\nimport { useEffect } from 'react';\n// form\nimport { useForm, Controller } from 'react-hook-form';\nimport { yupResolver } from '@hookform/resolvers/yup';\n// @mui\nimport { InputBase, Stack } from '@mui/material';\nimport { LoadingButton } from '@mui/lab';\n// hooks\nimport useAuth from '../../hooks/useAuth';\n// ----------------------------------------------------------------------\n\nexport default function VerifyCodeForm() {\n  const navigate = useNavigate();\n  const { otpVerify } = useAuth();\n  const { enqueueSnackbar } = useSnackbar();\n\n  const VerifyCodeSchema = Yup.object().shape({\n    code1: Yup.string().required('Code is required'),\n    code2: Yup.string().required('Code is required'),\n    code3: Yup.string().required('Code is required'),\n    code4: Yup.string().required('Code is required'),\n    code5: Yup.string().required('Code is required'),\n    code6: Yup.string().required('Code is required'),\n  });\n\n  const defaultValues = {\n    code1: '',\n    code2: '',\n    code3: '',\n    code4: '',\n    code5: '',\n    code6: '',\n  };\n\n  const {\n    watch,\n    control,\n    setValue,\n    handleSubmit,\n    formState: { isSubmitting, isValid },\n  } = useForm({\n    mode: 'onBlur',\n    resolver: yupResolver(VerifyCodeSchema),\n    defaultValues,\n  });\n\n  const values = watch();\n\n  useEffect(() => {\n    document.addEventListener('paste', handlePasteClipboard);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  const onSubmit = async (data) => {\n    try {\n      const afterLogin = (result)=>{\n        if(result.success){\n          enqueueSnackbar('Phone verification is success', {variant:'success'})\n          navigate('/');\n        }\n        else{\n          enqueueSnackbar('Your phonenumber is not registered', {variant:'error'})\n        }\n      }\n      (otpVerify(Object.values(data).join(''),afterLogin))\n      \n    } catch (error) {\n      console.error(error);\n    }\n  };\n\n  const handlePasteClipboard = (event) => {\n    let data = event?.clipboardData?.getData('Text') || '';\n\n    data = data.split('');\n\n    [].forEach.call(document.querySelectorAll('#field-code'), (node, index) => {\n      node.value = data[index];\n      const fieldIndex = `code${index + 1}`;\n      setValue(fieldIndex, data[index]);\n    });\n  };\n\n  const handleChangeWithNextField = (event, handleChange) => {\n    const { maxLength, value, name } = event.target;\n    const fieldIndex = name.replace('code', '');\n\n    const fieldIntIndex = Number(fieldIndex);\n\n    if (value.length >= maxLength) {\n      if (fieldIntIndex < 6) {\n        const nextfield = document.querySelector(`input[name=code${fieldIntIndex + 1}]`);\n\n        if (nextfield !== null) {\n          nextfield.focus();\n        }\n      }\n    }\n\n    handleChange(event);\n  };\n\n  return (\n    <>\n    <form onSubmit={handleSubmit(onSubmit)}>\n      <Stack direction=\"row\" spacing={3} mb={3} justifyContent=\"center\">\n        {Object.keys(values).map((name, index) => (\n          <Controller\n            key={name}\n            name={`code${index + 1}`}\n            control={control}\n            render={({ field }) => (\n              <InputBase\n                {...field}\n                id=\"field-code\"\n                autoFocus={index === 0}\n                placeholder=\"-\"\n                onChange={(event) => handleChangeWithNextField(event, field.onChange)}\n                inputProps={{\n                  maxLength: 1,\n                  sx: {\n                    py: 0.5,\n                    textAlign: 'center',\n                    width: { xs: 36, sm: 65 },\n                    height: { xs: 43, sm: 80 },\n                  },\n                }}\n                sx={{ fontSize: '2.3rem', bgcolor: 'grey.50016', borderRadius: 1, }}\n              />\n            )}\n          />\n        ))}\n\n      </Stack>\n      <LoadingButton\n        size=\"large\"\n        type=\"submit\"\n        variant=\"contained\"\n        loading={isSubmitting}\n        disabled={!isValid}\n        sx={{bgcolor:'grey.50016',border:'1px solid',borderColor:'grey.50048'}}\n      >\n        Next\n      </LoadingButton>\n    </form>\n    \n    </>    \n  );\n}\n", "import { Link as RouterLink } from 'react-router-dom';\n// @mui\nimport { styled } from '@mui/material/styles';\nimport { <PERSON>, Button, Link, Container, Typography } from '@mui/material';\n// layouts\nimport LogoOnlyLayout from '../../layout/LogoOnlyLayout';\n// components\nimport Page from '../../components/Page';\nimport Iconify from '../../components/Iconify';\nimport CarFront from '../../components/CarFront'\n// sections\nimport  VerifyCodeForm  from '../../sections/auth/VerifyCodeForm';\n// import Timer from '../../components/Timer';\n\n// ----------------------------------------------------------------------\n\nconst RootStyle = styled('div')(({ theme }) => ({\n  display: 'flex',\n  alignItems: 'center',\n  padding: theme.spacing(8, 0),\n  height:'100vh'\n}));\n\n// ----------------------------------------------------------------------\n\nexport default function VerifyCode() { \n  return (\n    <Page title=\"Verify\" sx={{ height: 1 }}>\n      <RootStyle>\n        <LogoOnlyLayout />\n\n        <Container>\n          <Box sx={{ maxWidth: 480, mx: 'auto', textAlign: 'center' }}>\n            <Button\n              size=\"small\"\n              component={RouterLink}\n              to={\"/auth/login\"}\n              startIcon={<Iconify icon={'eva:arrow-ios-back-fill'} width={20} height={20} />}\n              sx={{color:'text.primary' }}\n            >\n              Back\n            </Button>\n            <Box width={\"50%\"} sx={{ mx: 'auto', mb: 3}}>\n             <CarFront/>\n            </Box>\n            <Typography variant=\"h3\" paragraph >\n              Verification Code\n            </Typography>\n            <Typography  paragraph>\n              Please enter cerification code we sent to your phone number\n            </Typography>\n            <Box sx={{ mt: 5, mb: 3 }}>\n              <VerifyCodeForm />\n            </Box>\n            <Typography variant=\"body2\">\n              Haven't got your OTP number yet?  &nbsp;\n              <Link variant=\"subtitle2\" underline=\"none\" sx={{cursor:'pointer',color:'text.secondary'}} onClick={() => { }}>\n                Resend code\n              </Link>\n            </Typography>\n          </Box>\n          <div id = 'recaptcha-container' />\n        </Container>\n      </RootStyle>\n    </Page>\n  );\n}\n", "import PropTypes from 'prop-types';\n// icons\nimport { Icon } from '@iconify/react';\n// @mui\nimport { Box } from '@mui/material';\n\n// ----------------------------------------------------------------------\n\nIconify.propTypes = {\n  icon: PropTypes.oneOfType([PropTypes.element, PropTypes.string]),\n  sx: PropTypes.object,\n};\n\n\nexport default function Iconify({ icon, sx, ...other }) {\n  return <Box component={Icon} icon={icon} sx={{ ...sx }} {...other} />;\n}\n", "import PropTypes from 'prop-types';\nimport { Helmet } from 'react-helmet-async';\nimport { forwardRef } from 'react';\n// @mui\nimport { Box, Container } from '@mui/material';\n\n// ----------------------------------------------------------------------\n\nconst Page = forwardRef(({ children, title = '', meta, ...other }, ref) => (\n  <>\n    <Helmet>\n      <title>{title}</title>\n      {meta}\n    </Helmet>\n\n    <Box ref={ref} {...other}>\n      <Container  >\n        {children}\n      </Container>\n\n    </Box>\n  </>\n));\n\nPage.propTypes = {\n  children: PropTypes.node.isRequired,\n  title: PropTypes.string,\n  meta: PropTypes.node,\n};\n\nexport default Page;\n", "import PropTypes from 'prop-types';\nimport { Link as RouterLink } from 'react-router-dom';\n// @mui\nimport { useTheme } from '@mui/material/styles';\nimport { Box } from '@mui/material';\n\n// ----------------------------------------------------------------------\n\nCarFront.propTypes = {\n  disabledLink: PropTypes.bool,\n  sx: PropTypes.object,\n  color:PropTypes.string,\n};\n\n\n\nexport default function CarFront({ disabledLink = false, sx,color }) {\n  const theme = useTheme();\n  const PRIMARY_MAIN = color!==undefined?color:theme.palette.grey[500_48] ;\n  // theme.palette.primary.main;\n  \n  const svg = (\n    <Box sx={{ width: 'inherit', height: 'inherit', ...sx }}>\n\n\n      <svg version=\"1.0\" xmlns=\"http://www.w3.org/2000/svg\"\n        width=\"100%\" height=\"100%\" viewBox=\"0 0 220.000000 180.000000\"\n        preserveAspectRatio=\"xMidYMid meet\">\n\n        <g transform=\"translate(0.000000,229.000000) scale(0.100000,-0.100000)\"\n          fill={PRIMARY_MAIN} stroke=\"none\">\n          <path d=\"M714 1820 c-29 -4 -58 -11 -65 -16 -43 -25 -89 -69 -158 -150 l-78\n-91 -11 30 -11 30 -72 -6 c-149 -13 -160 -82 -18 -121 32 -10 59 -19 59 -21 0\n-2 -20 -13 -44 -25 -55 -26 -121 -96 -149 -158 -20 -43 -22 -66 -25 -272 -4\n-253 -1 -282 34 -317 17 -17 24 -35 24 -64 0 -29 7 -47 25 -64 21 -22 33 -25\n93 -25 86 0 111 16 119 78 l6 42 658 0 659 0 0 -25 c0 -33 25 -81 45 -89 9 -3\n47 -6 84 -6 83 0 111 22 111 87 0 32 7 48 30 73 l31 33 -3 256 c-3 244 -4 258\n-26 303 -30 60 -89 121 -147 151 l-46 23 58 18 c77 24 103 41 103 70 0 28 -27\n43 -101 54 -66 10 -99 1 -99 -28 0 -11 -3 -20 -8 -20 -4 0 -44 42 -88 93 -100\n115 -148 149 -223 158 -74 10 -702 9 -767 -1z m787 -60 c40 -11 127 -97 213\n-209 l50 -64 -49 6 c-211 29 -962 34 -1174 7 -46 -6 -86 -8 -89 -5 -12 12 180\n235 222 257 12 6 59 15 106 19 120 11 677 3 721 -11z m-147 -321 c28 -22 96\n-136 96 -161 0 -9 -7 -19 -16 -22 -9 -3 -161 -6 -339 -6 -378 0 -367 -3 -319\n87 16 30 43 71 60 89 l31 34 230 0 c217 0 232 -1 257 -21z m-952 -208 c84 -23\n159 -48 176 -61 32 -24 47 -59 32 -74 -4 -4 -90 -7 -189 -4 -216 5 -221 7\n-221 99 0 45 4 60 18 68 24 14 21 15 184 -28z m1596 9 c17 -34 8 -98 -18 -124\n-19 -20 -33 -21 -205 -24 -171 -4 -185 -3 -192 14 -5 13 4 27 35 54 36 29 65\n41 185 72 78 20 151 36 162 35 11 -1 25 -13 33 -27z m-1352 -288 c13 -8 84\n-146 84 -162 0 -11 -129 -14 -146 -2 -17 12 -103 156 -98 164 6 10 145 10 160\n0z m834 -9 c0 -10 -17 -49 -38 -88 l-37 -70 -295 -2 c-162 -2 -300 0 -306 5\n-13 8 -84 146 -84 162 0 7 127 10 380 10 355 0 380 -1 380 -17z m240 7 c0 -13\n-89 -153 -104 -162 -16 -11 -134 -10 -141 2 -6 10 48 124 73 153 12 13 31 17\n94 17 45 0 78 -4 78 -10z\"/>\n        </g>\n      </svg>\n\n    </Box>\n  );\n\n  if (disabledLink) {\n    return <>{svg}</>;\n  }\n\n  return <RouterLink to=\"/\">{svg}</RouterLink>;\n}\n"], "sourceRoot": ""}