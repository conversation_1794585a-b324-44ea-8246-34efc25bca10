const express = require("express");
const router = express.Router();
const ObjectId = require('mongoose').Types.ObjectId;
const licenseModel = require('../../models/license');
const userModel = require('../../models/user');
const Generator = require('license-key-generator');
const { getEbarimt, QPay } = require("../../utils/QPayment");
const INVOICE_VS_REAL_INVOICE_ID = require('../../utils/QPayment').INVOICE_VS_REAL_INVOICE_ID;
const Order = require('../../models/order');
const { sendSms } = require("../../utils/channel");
const wallet = require("../../models/wallet");
const axios = require('axios');
const jwt = require('jsonwebtoken');

/******************* */

router.post('/ebarimt', getEbarimt);

router.get(
    '/qpay/:page/:invoice/:cost/:sender/:phoneNumber',
    async (req, res) => {

        try {
            console.log(req.params, " is payment hook");

            const { invoice, sender, cost, page, phoneNumber } = req.params;
            if (page == 'license') {
                // Calculate days correctly: cost / price_per_month = months, then * 30 days per month
                const pricePerMonth = parseFloat(process.env.PRICE_PER_MONTH) || 5000;
                const months = parseFloat(cost) / pricePerMonth;
                const originalDays = months * 30; // 30 days per month

                // Add 1-month (30 days) bonus only for 6 months or more
                let bonusDays = 0;
                let bonusApplied = false;
                let bonusReason = "";

                if (months >= 6) {
                    bonusDays = 30;
                    bonusApplied = true;
                    bonusReason = "1-month extension bonus (6+ months)";
                    console.log(`BONUS_LOG: User ${sender} - License Extension Bonus Applied (${months} months >= 6)`);
                } else {
                    console.log(`BONUS_LOG: User ${sender} - No bonus applied (${months} months < 6)`);
                }

                const days = originalDays + bonusDays;
                console.log(`BONUS_LOG: Original days: ${originalDays}, Bonus days: ${bonusDays}, Total days: ${days}`);

                const user = await userModel.findById(sender);
                if (!user) {
                    return;
                }

                // Check if license already exists for this invoice to prevent duplicates
                const existingLicense = await licenseModel.findOne({ invoice });
                if (existingLicense) {
                    return;
                }

                const options = {
                    type: "random",
                    length: 12,
                    group: 3,
                    split: '-',
                    splitStatus: true
                }
                const code = new Generator(options);

                // Use Promise instead of callback to avoid timing issues
                try {
                    const licenseKey = await new Promise((resolve, reject) => {
                        code.get((err, key) => {
                            if (err) reject(err);
                            else resolve(key);
                        });
                    });

                    // Calculate new expiration date
                    let currentExpiry = Date.now();
                    if (user.expired && new Date(user.expired).getTime() > Date.now()) {
                        // If user has valid license, extend from current expiry
                        currentExpiry = new Date(user.expired).getTime();
                    }

                    const newExpiry = currentExpiry + (days * 24 * 60 * 60 * 1000); // days to milliseconds
                    const expiryDate = new Date(newExpiry);

                    // Create license record with bonus tracking
                    const license = new licenseModel({
                        owner: ObjectId(sender),
                        invoice: invoice,
                        cost: parseFloat(cost),
                        licenseKey: licenseKey,
                        expired: expiryDate,
                        realInvoice: INVOICE_VS_REAL_INVOICE_ID[invoice] || '',
                        createdAt: new Date(), // Ensure createdAt is set for income tracking
                        // Bonus tracking fields
                        bonusApplied: bonusApplied,
                        bonusDays: bonusDays,
                        bonusReason: bonusReason,
                        originalDays: originalDays,
                        totalDaysGranted: days
                    });

                    await license.save();

                    // Update user
                    user.expired = expiryDate;
                    user.licenseKey = licenseKey;
                    user.status = "active";
                    await user.save();

                    // Send SMS notification
                    try {
                        const sms = {
                            mobile: user.phoneNumber,
                            sms: `sain bn u, tani license amjilttai sungagdlaa www.aslaa.mn`
                        };
                        await sendSms(sms, {}, res);
                    } catch (smsError) {
                        console.error('SMS sending failed:', smsError);
                    }

                } catch (error) {
                    console.error('License creation failed:', error);
                }
            }
            if (page == 'order') {
                const order = await Order.findOne({ phoneNumber });
                if (order) {
                    order.paid = true;
                    order.invoiceId = invoice;
                    order.realInvoiceId = INVOICE_VS_REAL_INVOICE_ID[invoice];
                    await order.save();


                    const sms = {
                        mobile: phoneNumber,
                        sms: ''
                    }
                    sms.sms = `sain bn u, tani ${order.CarModel} mashind asaaltin tohooromj suuriluulah zahialga batalgaajlaa www.aslaa.mn`

                    const response = await sendSms(sms, {}, res);
                }
            }
            if (page == 'balance') {
                const user = await userModel.findById(sender);
                user.balance = (user?.balance || 0) + parseInt(cost);
                await user.save();
                wallet.updateOne(
                    {
                        user: sender,
                    },
                    {
                        $set: { user: sender, currentBalance: user.balance, },
                        $push: { transactions: { ts: Date.now(), mode: 'deposit', description: 'QPay Hook', before: (user.balance - parseInt(cost)), amount: parseInt(cost), invoice, realInvoiceId: INVOICE_VS_REAL_INVOICE_ID[invoice] } }
                    },
                    {
                        upsert: true
                    }
                ).then(res)
            }


        } catch (err) {
            console.log(err);

        }
        res.status(200).json({ success: true });
    }
)


// This route should be accessible at /api/hook/payment/check/:invoiceId
router.get('/check/:invoiceId', async (req, res) => {
    try {
        const { invoiceId } = req.params;
        
        // First get the real invoice ID from our mapping
        const realInvoiceId = INVOICE_VS_REAL_INVOICE_ID[invoiceId];
        
        if (!realInvoiceId) {
            return res.status(404).json({
                success: false,
                message: "Invoice ID not found in our records"
            });
        }

        // Get token
        const token = await QPay().getAuthToken();
        if (!token) {
            return res.status(401).json({
                success: false,
                message: "Could not get QPay token"
            });
        }

        // Check payment using real invoice ID
        const paymentCheck = await QPay().paymentCheck(token, realInvoiceId);
        
        if (paymentCheck.status === 200) {
            return res.json({
                success: true,
                payment: paymentCheck.result,
                invoiceId: invoiceId,
                realInvoiceId: realInvoiceId
            });
        }

        return res.status(404).json({
            success: false,
            message: "Payment not found",
            invoiceId: invoiceId,
            realInvoiceId: realInvoiceId
        });

    } catch (error) {
        console.error("Manual payment check error:", error);
        return res.status(500).json({
            success: false,
            error: error.message,
            invoiceId: req.params.invoiceId
        });
    }
});

// Manual license creation endpoint for when payment is confirmed
router.post('/create-license-manual', async (req, res) => {
    try {
        const { userId, cost, invoiceId, platform, verificationMethod } = req.body;

        console.log('Manual license creation request:', { userId, cost, invoiceId, platform, verificationMethod });

        if (!cost || !invoiceId) {
            return res.status(400).json({
                success: false,
                message: "Missing required fields: cost, invoiceId"
            });
        }

        // Get user ID - either from request body or from token
        let actualUserId = userId;

        // If userId is not provided or is 'unknown', try to get from token
        if (!actualUserId || actualUserId === 'unknown' || actualUserId === 'current_user') {
            // Extract user ID from token (you'll need to implement token verification)
            const token = req.headers.authorization?.replace('Bearer ', '');
            if (token) {
                try {
                    // You'll need to implement this function to decode the JWT token
                    const decoded = jwt.verify(token, process.env.JWT_SECRET);
                    actualUserId = decoded.id || decoded.userId;
                    console.log('Extracted userId from token:', actualUserId);
                } catch (tokenError) {
                    console.error('Token verification failed:', tokenError);
                    return res.status(401).json({
                        success: false,
                        message: "Invalid token"
                    });
                }
            }
        }

        if (!actualUserId) {
            return res.status(400).json({
                success: false,
                message: "Could not determine user ID"
            });
        }

        // Calculate days correctly: cost / price_per_month = months, then * 30 days per month
        const pricePerMonth = parseFloat(process.env.PRICE_PER_MONTH) || 5000;
        const months = parseFloat(cost) / pricePerMonth;
        const originalDays = Math.round(months * 30); // 30 days per month, rounded

        // Add 1-month (30 days) bonus only for 6 months or more
        let bonusDays = 0;
        let bonusApplied = false;
        let bonusReason = "";

        if (months >= 6) {
            bonusDays = 30;
            bonusApplied = true;
            bonusReason = "1-month extension bonus (6+ months)";
            console.log(`BONUS_LOG: User ${actualUserId} - License Extension Bonus Applied (${months} months >= 6)`);
        } else {
            console.log(`BONUS_LOG: User ${actualUserId} - No bonus applied (${months} months < 6)`);
        }

        const days = originalDays + bonusDays;

        console.log(`License calculation: ${cost} / ${pricePerMonth} = ${months} months = ${originalDays} days`);
        console.log(`Adding bonus: ${bonusDays} days. Total days: ${days}`);
        console.log(`BONUS_LOG: Invoice: ${invoiceId}, Cost: ${cost}, Original days: ${originalDays}, Bonus days: ${bonusDays}, Total days: ${days}`);

        console.log(`Looking up user with ID: ${actualUserId}`);
        const user = await userModel.findById(actualUserId);

        if (!user) {
            console.error(`User not found with ID: ${actualUserId}`);
            return res.status(404).json({
                success: false,
                message: "User not found"
            });
        }

        console.log(`User found:`, {
            id: user._id,
            phoneNumber: user.phoneNumber,
            currentExpired: user.expired,
            currentLicenseKey: user.licenseKey,
            currentStatus: user.status
        });

        // Check if license already exists for this invoice
        const existingLicense = await licenseModel.findOne({ invoice: invoiceId });

        if (existingLicense) {
            return res.json({
                success: true,
                message: "License already exists for this invoice",
                license: existingLicense
            });
        }

        const options = {
            type: "random",
            length: 12,
            group: 3,
            split: '-',
            splitStatus: true
        }
        const code = new Generator(options);

        const licenseKey = await new Promise((resolve, reject) => {
            code.get((err, key) => {
                if (err) reject(err);
                else resolve(key);
            });
        });

        // Calculate new expiration date correctly
        let currentExpiry = Date.now();
        console.log(`Current time: ${new Date(currentExpiry).toISOString()}`);
        console.log(`User current expiry: ${user.expired}`);

        if (user.expired && new Date(user.expired).getTime() > Date.now()) {
            // If user has valid license, extend from current expiry
            currentExpiry = new Date(user.expired).getTime();
            console.log(`Using existing expiry as base: ${new Date(currentExpiry).toISOString()}`);
        } else {
            console.log(`Using current time as base: ${new Date(currentExpiry).toISOString()}`);
        }

        const newExpiry = currentExpiry + (days * 24 * 60 * 60 * 1000); // days to milliseconds
        const expiryDate = new Date(newExpiry);
        console.log(`Calculated new expiry: ${expiryDate.toISOString()} (added ${days} days)`);

        // Create license record with bonus tracking
        const license = new licenseModel({
            owner: ObjectId(actualUserId),
            invoice: invoiceId,
            cost: parseFloat(cost),
            licenseKey: licenseKey,
            expired: expiryDate,
            realInvoice: INVOICE_VS_REAL_INVOICE_ID[invoiceId] || '',
            createdAt: new Date(), // Ensure createdAt is set for income tracking
            // Bonus tracking fields
            bonusApplied: bonusApplied,
            bonusDays: bonusDays,
            bonusReason: bonusReason,
            originalDays: originalDays,
            totalDaysGranted: days
        });

        await license.save();

        // Update user with new expiry
        console.log(`Updating user ${actualUserId} with new expiry: ${expiryDate}`);
        console.log(`Previous user expiry: ${user.expired}`);

        user.expired = expiryDate;
        user.licenseKey = licenseKey;
        user.status = "active";

        console.log(`About to save user with:`, {
            id: actualUserId,
            newExpired: expiryDate.toISOString(),
            newLicenseKey: licenseKey,
            newStatus: "active"
        });

        try {
            const savedUser = await user.save();
            console.log(`User saved successfully. Response:`, {
                id: savedUser._id,
                expired: savedUser.expired,
                licenseKey: savedUser.licenseKey,
                status: savedUser.status
            });

            // Verify the user was actually updated by fetching fresh from DB
            const verifyUser = await userModel.findById(actualUserId);
            console.log(`DB Verification after save:`, {
                id: verifyUser._id,
                expired: verifyUser.expired,
                licenseKey: verifyUser.licenseKey,
                status: verifyUser.status
            });
        } catch (saveError) {
            console.error(`Error saving user:`, saveError);
            throw new Error(`Failed to save user: ${saveError.message}`);
        }

        // Send SMS notification
        try {
            const sms = {
                mobile: user.phoneNumber,
                sms: `sain bn u, tani license amjilttai sungagdlaa www.aslaa.mn`
            };
            await sendSms(sms, {}, res);
        } catch (smsError) {
            console.log('SMS sending failed:', smsError);
        }

        console.log(`License created successfully for user ${actualUserId}:`);
        console.log(`- License ID: ${license._id}`);
        console.log(`- License Key: ${license.licenseKey}`);
        console.log(`- Cost: ${license.cost}`);
        console.log(`- Days Added: ${days}`);
        console.log(`- Previous Expiry: ${user.expired}`);
        console.log(`- New Expiry: ${license.expired}`);
        console.log(`- Invoice ID: ${invoiceId}`);

        res.json({
            success: true,
            message: "License created successfully",
            daysAdded: days,
            newExpiryDate: license.expired,
            previousExpiryDate: user.expired,
            license: {
                id: license._id,
                licenseKey: license.licenseKey,
                expired: license.expired,
                cost: license.cost
            },
            calculation: {
                cost: parseFloat(cost),
                pricePerMonth: pricePerMonth,
                months: months,
                days: days
            }
        });

    } catch (error) {
        console.error("Manual license creation error:", error);
        res.status(500).json({
            success: false,
            message: "Failed to create license",
            error: error.message
        });
    }
});




// Test endpoint to manually extend license for debugging
router.post('/test-extend-license', async (req, res) => {
    try {
        const { userId, days } = req.body;

        if (!userId || !days) {
            return res.status(400).json({
                success: false,
                message: "Missing userId or days"
            });
        }

        // Add 1-month (30 days) bonus only for 6 months or more (180+ days)
        const originalDays = days;
        const months = originalDays / 30; // Convert days to months
        let bonusDays = 0;
        let bonusApplied = false;

        if (months >= 6) {
            bonusDays = 30;
            bonusApplied = true;
            console.log(`TEST: Extending license for user ${userId} - Bonus Applied (${months} months >= 6)`);
        } else {
            console.log(`TEST: Extending license for user ${userId} - No bonus (${months} months < 6)`);
        }

        const totalDays = originalDays + bonusDays;

        console.log(`TEST: Original: ${originalDays} days, Bonus: ${bonusDays} days, Total: ${totalDays} days`);

        const user = await userModel.findById(userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: "User not found"
            });
        }

        console.log(`TEST: Current user expiry: ${user.expired}`);

        // Calculate new expiry
        let currentExpiry = Date.now();
        if (user.expired && new Date(user.expired).getTime() > Date.now()) {
            currentExpiry = new Date(user.expired).getTime();
        }

        const newExpiry = currentExpiry + (totalDays * 24 * 60 * 60 * 1000);
        const expiryDate = new Date(newExpiry);

        console.log(`TEST: New expiry will be: ${expiryDate.toISOString()}`);

        // Update user
        user.expired = expiryDate;
        const savedUser = await user.save();

        console.log(`TEST: User saved with expiry: ${savedUser.expired}`);

        res.json({
            success: true,
            message: bonusApplied ? "License extended successfully with bonus" : "License extended successfully",
            previousExpiry: user.expired,
            newExpiry: expiryDate.toISOString(),
            originalDaysAdded: originalDays,
            bonusDaysAdded: bonusDays,
            totalDaysAdded: totalDays,
            bonusApplied: bonusApplied,
            months: months
        });

    } catch (error) {
        console.error('TEST: Error extending license:', error);
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

module.exports = router;
