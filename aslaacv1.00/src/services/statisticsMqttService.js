import mqttService from './mqttService';

class StatisticsMqttService {
  constructor() {
    this.isConnected = false;
    this.listeners = new Map();
    this.subscribedTopics = new Set();
    this.commandTracking = new Map(); // Track commands sent vs responses received
    this.statisticsTopics = {
      commands: 'statistics/commands',
      devices: 'statistics/devices',
      users: 'statistics/users',
      realtime: 'statistics/realtime'
    };
  }

  // Connect to MQTT for statistics
  connect(user) {
    if (this.isConnected) {
      return;
    }

    try {
      // Use existing MQTT service
      if (mqttService.isConnected) {
        this.setupMqttHandlers();
        this.isConnected = true;
        this.emit('connected');
      } else {
        // Listen for MQTT connection
        mqttService.on('connect', () => {
          this.setupMqttHandlers();
          this.isConnected = true;
          this.emit('connected');
        });
      }
    } catch (error) {
      console.error('Statistics MQTT: Connection error', error);
    }
  }

  // Setup MQTT message handlers
  setupMqttHandlers() {
    // Set up message handler for all topics
    mqttService.on('message', (topic, message) => {
      try {
        // Handle device command responses (deviceNumber/msg pattern)
        if (topic.includes('/msg')) {
          this.handleDeviceResponse(topic, message);
        }
        // Handle statistics topics
        else if (topic.startsWith('statistics/')) {
          const data = JSON.parse(message.toString());
          console.log(`Statistics MQTT: Received message on ${topic}`, data);

          // Emit specific events based on topic
          if (topic === this.statisticsTopics.commands) {
            this.emit('command-statistics', data);
          } else if (topic === this.statisticsTopics.devices) {
            this.emit('device-statistics', data);
          } else if (topic === this.statisticsTopics.users) {
            this.emit('user-statistics', data);
          } else if (topic === this.statisticsTopics.realtime) {
            this.emit('realtime-update', data);
          }

          // Also emit a general statistics update event
          this.emit('statistics-update', { topic, data });
        }
      } catch (error) {
        console.error(`Statistics MQTT: Error processing message on ${topic}`, error);
      }
    });

    // Handle MQTT connection events
    mqttService.on('connect', () => {
      console.log('Statistics MQTT: Connected');
      this.isConnected = true;

      // Resubscribe to topics if needed
      this.subscribedTopics.forEach(topic => {
        mqttService.subscribe(topic);
      });

      this.emit('connected');
    });

    mqttService.on('disconnect', () => {
      console.log('Statistics MQTT: Disconnected');
      this.isConnected = false;
      this.emit('disconnected');
    });

    mqttService.on('error', (error) => {
      console.error('Statistics MQTT: Error', error);
      this.emit('error', error);
    });
  }

  // Handle device response messages
  handleDeviceResponse(topic, message) {
    try {
      const deviceNumber = topic.replace('/msg', '');
      const responseData = message.toString();

      console.log(`Statistics MQTT: Device response from ${deviceNumber}`, responseData);

      // Check if we have a tracked command for this device
      const commandKey = this.findCommandForDevice(deviceNumber);
      if (commandKey) {
        const commandData = this.commandTracking.get(commandKey);
        const responseTime = Date.now() - commandData.sentTime;

        // Create command statistics update
        const statsUpdate = {
          type: 'command_response',
          data: {
            userId: commandData.userId,
            deviceNumber: deviceNumber,
            command: commandData.command,
            commandType: commandData.commandType,
            success: this.isSuccessResponse(responseData),
            responseTime: responseTime,
            response: responseData,
            sentTime: commandData.sentTime,
            receiveTime: Date.now()
          }
        };

        // Emit command response event
        this.emit('command-response', statsUpdate);

        // Remove from tracking
        this.commandTracking.delete(commandKey);

        // Publish to statistics topic
        this.publishCommandUpdate(statsUpdate.data);
      }
    } catch (error) {
      console.error('Statistics MQTT: Error handling device response', error);
    }
  }

  // Track command sent to device
  trackCommandSent(userId, deviceNumber, command, commandType = 'other') {
    const commandKey = `${deviceNumber}_${Date.now()}`;
    const commandData = {
      userId,
      deviceNumber,
      command,
      commandType,
      sentTime: Date.now()
    };

    this.commandTracking.set(commandKey, commandData);

    // Set timeout to handle no response scenario
    setTimeout(() => {
      if (this.commandTracking.has(commandKey)) {
        const timeoutData = this.commandTracking.get(commandKey);
        const statsUpdate = {
          type: 'command_timeout',
          data: {
            ...timeoutData,
            success: false,
            responseStatus: 'timeout',
            failureReason: 'Device did not respond within timeout period',
            responseTime: Date.now() - timeoutData.sentTime
          }
        };

        this.emit('command-timeout', statsUpdate);
        this.publishCommandUpdate(statsUpdate.data);
        this.commandTracking.delete(commandKey);
      }
    }, 30000); // 30 second timeout

    // Emit command sent event
    const sentUpdate = {
      type: 'command_sent',
      data: commandData
    };

    this.emit('command-sent', sentUpdate);
    this.publishCommandUpdate(commandData);

    return commandKey;
  }

  // Find command for device response
  findCommandForDevice(deviceNumber) {
    for (const [key, command] of this.commandTracking.entries()) {
      if (command.deviceNumber === deviceNumber) {
        return key;
      }
    }
    return null;
  }

  // Determine if response indicates success
  isSuccessResponse(response) {
    // Customize this based on your device response patterns
    const successIndicators = ['OK', 'SUCCESS', 'DONE', 'ACK'];
    const errorIndicators = ['ERROR', 'FAIL', 'TIMEOUT', 'INVALID'];

    const upperResponse = response.toUpperCase();

    if (successIndicators.some(indicator => upperResponse.includes(indicator))) {
      return true;
    }

    if (errorIndicators.some(indicator => upperResponse.includes(indicator))) {
      return false;
    }

    // Default to success if response is received (customize as needed)
    return true;
  }

  // Subscribe to statistics updates for specific filters
  subscribeToStatistics(filters = {}) {
    if (!mqttService.isConnected) {
      console.warn('Statistics MQTT: Not connected, cannot subscribe');
      return;
    }

    // Subscribe to all statistics topics
    Object.values(this.statisticsTopics).forEach(topic => {
      if (!this.subscribedTopics.has(topic)) {
        mqttService.subscribe(topic);
        this.subscribedTopics.add(topic);
        console.log(`Statistics MQTT: Subscribed to ${topic}`);
      }
    });

    // Subscribe to device response topics for active devices
    if (filters.deviceNumbers && Array.isArray(filters.deviceNumbers)) {
      filters.deviceNumbers.forEach(deviceNumber => {
        const responseTopic = `${deviceNumber}/msg`;
        if (!this.subscribedTopics.has(responseTopic)) {
          mqttService.subscribe(responseTopic);
          this.subscribedTopics.add(responseTopic);
          console.log(`Statistics MQTT: Subscribed to device responses ${responseTopic}`);
        }
      });
    }

    // If filters are provided, publish them to request filtered updates
    if (Object.keys(filters).length > 0) {
      this.publishFilters(filters);
    }
  }

  // Subscribe to specific device responses
  subscribeToDeviceResponses(deviceNumbers) {
    if (!mqttService.isConnected) {
      console.warn('Statistics MQTT: Not connected, cannot subscribe to device responses');
      return;
    }

    deviceNumbers.forEach(deviceNumber => {
      const responseTopic = `${deviceNumber}/msg`;
      if (!this.subscribedTopics.has(responseTopic)) {
        mqttService.subscribe(responseTopic);
        this.subscribedTopics.add(responseTopic);
        console.log(`Statistics MQTT: Subscribed to device responses ${responseTopic}`);
      }
    });
  }

  // Unsubscribe from statistics updates
  unsubscribeFromStatistics() {
    if (!mqttService.isConnected) {
      return;
    }

    // Unsubscribe from all statistics topics
    this.subscribedTopics.forEach(topic => {
      mqttService.unsubscribe(topic);
      console.log(`Statistics MQTT: Unsubscribed from ${topic}`);
    });

    this.subscribedTopics.clear();
  }

  // Publish filter preferences to the server
  publishFilters(filters) {
    if (!mqttService.isConnected) {
      console.warn('Statistics MQTT: Not connected, cannot publish filters');
      return;
    }

    const filterMessage = {
      type: 'filter_update',
      filters,
      timestamp: Date.now()
    };

    mqttService.publish('statistics/filters', JSON.stringify(filterMessage));
    console.log('Statistics MQTT: Published filters', filters);
  }

  // Request real-time statistics refresh
  requestStatisticsRefresh(filters = {}) {
    if (!mqttService.isConnected) {
      console.warn('Statistics MQTT: Not connected, cannot request refresh');
      return;
    }

    const refreshMessage = {
      type: 'refresh_request',
      filters,
      timestamp: Date.now()
    };

    mqttService.publish('statistics/refresh', JSON.stringify(refreshMessage));
    console.log('Statistics MQTT: Requested refresh with filters', filters);
  }

  // Add event listener
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  // Remove event listener
  off(event, callback) {
    if (!this.listeners.has(event)) {
      return;
    }

    const listeners = this.listeners.get(event);
    const index = listeners.indexOf(callback);
    if (index > -1) {
      listeners.splice(index, 1);
    }
  }

  // Emit event to listeners
  emit(event, data) {
    if (!this.listeners.has(event)) {
      return;
    }

    this.listeners.get(event).forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`Statistics MQTT: Error in event listener for ${event}`, error);
      }
    });
  }

  // Disconnect from MQTT statistics
  disconnect() {
    this.unsubscribeFromStatistics();
    this.isConnected = false;
    this.listeners.clear();
    this.subscribedTopics.clear();
    this.commandTracking.clear();
  }

  // Get connection status
  get connected() {
    return this.isConnected && mqttService.isConnected;
  }

  // Publish command statistics update
  publishCommandUpdate(commandData) {
    if (!mqttService.isConnected) {
      return;
    }

    const message = {
      type: 'command_update',
      data: commandData,
      timestamp: Date.now()
    };

    mqttService.publish(this.statisticsTopics.commands, JSON.stringify(message));
  }

  // Publish device statistics update
  publishDeviceUpdate(deviceData) {
    if (!mqttService.isConnected) {
      return;
    }

    const message = {
      type: 'device_update',
      data: deviceData,
      timestamp: Date.now()
    };

    mqttService.publish(this.statisticsTopics.devices, JSON.stringify(message));
  }

  // Publish user activity update
  publishUserUpdate(userData) {
    if (!mqttService.isConnected) {
      return;
    }

    const message = {
      type: 'user_update',
      data: userData,
      timestamp: Date.now()
    };

    mqttService.publish(this.statisticsTopics.users, JSON.stringify(message));
  }

  // Publish real-time statistics update
  publishRealtimeUpdate(statsData) {
    if (!mqttService.isConnected) {
      return;
    }

    const message = {
      type: 'realtime_stats',
      data: statsData,
      timestamp: Date.now()
    };

    mqttService.publish(this.statisticsTopics.realtime, JSON.stringify(message));
  }
}

// Create singleton instance
const statisticsMqttService = new StatisticsMqttService();

export default statisticsMqttService;
