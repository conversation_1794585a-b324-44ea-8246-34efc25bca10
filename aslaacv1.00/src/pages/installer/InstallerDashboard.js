import { useState, useCallback } from "react";
import {
  Container,
  Typo<PERSON>,
  <PERSON>,
  Card,
  CardContent,
  Button,
  TextField,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Snackbar,
  Alert,
  CircularProgress,
  useMediaQuery,
  useTheme,
  Chip,
} from "@mui/material";
import { styled } from "@mui/system";
import { useTranslation } from "react-i18next";
import axios from "../../utils/axios";
import logger from "../../utils/logger";
import AddIcon from "@mui/icons-material/Add";
import SearchIcon from "@mui/icons-material/Search";
import LockResetIcon from "@mui/icons-material/LockReset";
import DevicesIcon from "@mui/icons-material/Devices";
import PersonAddIcon from "@mui/icons-material/PersonAdd";
import Page from "../../components/Page";
import Layout from "../../layout";

// Mobile-first responsive container
const StyledContainer = styled(Container)(({ theme }) => ({
  paddingTop: theme.spacing(8),
  paddingBottom: theme.spacing(4),
  display: "flex",
  flexDirection: "column",
  gap: theme.spacing(2),
  [theme.breakpoints.up('md')]: {
    paddingTop: theme.spacing(12),
  },
}));

// Mobile-friendly action button
const MobileActionButton = styled(Button)(({ theme }) => ({
  [theme.breakpoints.down('sm')]: {
    width: '100%',
    marginTop: theme.spacing(1),
  },
}));

export default function InstallerDashboard() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { t } = useTranslation();

  // State management
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResponse, setSearchResponse] = useState(null);
  const [isSearching, setIsSearching] = useState(false);
  
  // Dialog states
  const [openAddUserDialog, setOpenAddUserDialog] = useState(false);
  const [openAddDeviceDialog, setOpenAddDeviceDialog] = useState(false);
  const [openResetPinDialog, setOpenResetPinDialog] = useState(false);
  const [openDeleteDeviceDialog, setOpenDeleteDeviceDialog] = useState(false);
  
  // Form states
  const [newUserPhone, setNewUserPhone] = useState("");
  const [newDeviceNumber, setNewDeviceNumber] = useState("");
  const [resetPinPhone, setResetPinPhone] = useState("");
  const [selectedUser, setSelectedUser] = useState(null);
  const [selectedDeviceNumber, setSelectedDeviceNumber] = useState("");
  
  // Loading states
  const [isAddingUser, setIsAddingUser] = useState(false);
  const [isAddingDevice, setIsAddingDevice] = useState(false);
  const [isResettingPin, setIsResettingPin] = useState(false);
  const [isDeletingDevice, setIsDeletingDevice] = useState(false);

  // Notification state
  const [notification, setNotification] = useState({
    open: false,
    message: "",
    severity: "success",
  });

  const showNotification = useCallback((message, severity = "success") => {
    setNotification({ open: true, message, severity });
  }, []);

  const hideNotification = useCallback(() => {
    setNotification(prev => ({ ...prev, open: false }));
  }, []);

  // Search functionality
  const handleSearch = useCallback(async () => {
    const trimmedQuery = searchQuery.trim();
    if (!trimmedQuery) {
      showNotification(t("installer.notifications.search_term_required"), "warning");
      return;
    }

    setIsSearching(true);
    try {
      const paramObj = {};
      // If query contains only digits and is 14+ characters, treat as device number
      if (/^\d+$/.test(trimmedQuery) && trimmedQuery.length >= 14) {
        paramObj.deviceNumber = trimmedQuery;
      } else {
        paramObj.phoneNumber = trimmedQuery;
      }

      const response = await axios.get("/api/admin/user/list", { params: paramObj });

      if (response.data.success) {
        const uniqueUsers = response.data.users.reduce((acc, user) => {
          if (!acc.some((u) => u.phoneNumber === user.phoneNumber)) {
            acc.push(user);
          }
          return acc;
        }, []);

        // Process the response data
        const processedResponse = {
          ...response.data,
          users: uniqueUsers,
          // Ensure lastPayload and lastSimcardLog are arrays matching users length
          lastPayload: Array.isArray(response.data.lastPayload) ? response.data.lastPayload : [],
          lastSimcardLog: Array.isArray(response.data.lastSimcardLog) ? response.data.lastSimcardLog : []
        };

        setSearchResponse(processedResponse);

        if (uniqueUsers.length === 0) {
          showNotification(t("installer.notifications.no_users_found"), "info");
        }
      } else {
        showNotification(t("installer.notifications.search_failed"), "error");
      }
    } catch (error) {
      logger.error("Error searching users:", error);
      showNotification(t("installer.notifications.search_connection_error"), "error");
    } finally {
      setIsSearching(false);
    }
  }, [searchQuery, showNotification, t]);

  // Add user functionality
  const handleAddUser = useCallback(async () => {
    if (!newUserPhone.trim()) return;

    setIsAddingUser(true);
    try {
      const response = await axios.post("/api/auth/admin-register", { 
        phoneNumber: newUserPhone.trim() 
      });
      
      if (response.data.success) {
        showNotification(t("installer.notifications.user_added"), "success");
        setNewUserPhone("");
        setOpenAddUserDialog(false);
      } else {
        showNotification(response.data.message || t("installer.notifications.user_add_failed"), "error");
      }
    } catch (error) {
      logger.error("Error adding user:", error);
      showNotification(t("installer.notifications.user_add_error"), "error");
    } finally {
      setIsAddingUser(false);
    }
  }, [newUserPhone, showNotification, t]);

  // Add device functionality
  const handleAddDevice = useCallback(async () => {
    if (!selectedUser || !newDeviceNumber.trim()) return;

    setIsAddingDevice(true);
    try {
      const response = await axios.post("/api/device/admin-create", {
        deviceNumber: newDeviceNumber.trim(),
        phoneNumber: selectedUser.phoneNumber,
        uix: "CarV1.2",
        type: "4g",
        isDefault: true,
        deviceName: `Device for ${selectedUser.phoneNumber}`
      });
      
      if (response.data.success) {
        showNotification(t("installer.notifications.device_created"), "success");
        setNewDeviceNumber("");
        setOpenAddDeviceDialog(false);
        // Refresh search if there's a query
        if (searchQuery) {
          handleSearch();
        }
      } else {
        showNotification(response.data.message || t("installer.notifications.device_create_failed"), "error");
      }
    } catch (error) {
      logger.error("Error creating device:", error);
      showNotification(t("installer.notifications.device_create_error"), "error");
    } finally {
      setIsAddingDevice(false);
    }
  }, [selectedUser, newDeviceNumber, showNotification, searchQuery, handleSearch, t]);

  // Reset PIN functionality
  const handleResetPin = useCallback(async () => {
    if (!resetPinPhone.trim()) return;

    setIsResettingPin(true);
    try {
      const response = await axios.post("/api/admin/user/reset-pin", {
        phoneNumber: resetPinPhone.trim()
      });
      
      if (response.data.success) {
        showNotification(t("installer.notifications.pin_reset"), "success");
        setResetPinPhone("");
        setOpenResetPinDialog(false);
      } else {
        showNotification(response.data.message || t("installer.notifications.pin_reset_failed"), "error");
      }
    } catch (error) {
      logger.error("Error resetting PIN:", error);
      showNotification(t("installer.notifications.pin_reset_error"), "error");
    } finally {
      setIsResettingPin(false);
    }
  }, [resetPinPhone, showNotification, t]);

  // Delete device functionality
  const handleDeleteDevice = useCallback(async () => {
    if (!selectedDeviceNumber) return;

    setIsDeletingDevice(true);
    try {
      const response = await axios.post("/api/device/delete", {
        deviceNumber: selectedDeviceNumber
      });

      if (response.data.success) {
        showNotification(t("installer.notifications.device_deleted"), "success");
        setOpenDeleteDeviceDialog(false);
        // Refresh search if there's a query
        if (searchQuery) {
          handleSearch();
        }
      } else {
        showNotification(response.data.message || t("installer.notifications.device_delete_failed"), "error");
      }
    } catch (error) {
      logger.error("Error deleting device:", error);
      showNotification(t("installer.notifications.device_delete_error"), "error");
    } finally {
      setIsDeletingDevice(false);
    }
  }, [selectedDeviceNumber, showNotification, searchQuery, handleSearch, t]);

  // Helper function to parse SIM data
  const parseSimData = useCallback((simLog) => {
    if (!simLog || !simLog.lastSimcardLog) return null;

    try {
      // Extract date and balance from SIM log content
      const content = simLog.lastSimcardLog || '';

      // Try different date formats
      const dateMatch = content.match(/(\d{4}-\d{2}-\d{2})|(\d{2}\/\d{2}\/\d{4})|(\d{2}\.\d{2}\.\d{4})/);

      // Try different balance formats
      const balanceMatch = content.match(/(\d+(?:\.\d+)?)\s*(?:MNT|₮|tugrik)/i) ||
                          content.match(/balance[:\s]*(\d+(?:\.\d+)?)/i) ||
                          content.match(/(\d+(?:\.\d+)?)\s*(?:төгрөг)/i);

      return {
        date: dateMatch ? (dateMatch[1] || dateMatch[2] || dateMatch[3]) : null,
        balance: balanceMatch ? balanceMatch[1] : null,
        fullContent: content,
        receivedAt: simLog.lastSimcardLogReceivedAt
      };
    } catch (error) {
      return null;
    }
  }, []);

  // Helper function to format date
  const formatDate = useCallback((dateString) => {
    if (!dateString) return t("installer.no_data");

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString();
    } catch (error) {
      return t("installer.no_data");
    }
  }, [t]);

  // Helper function to check if user is expired
  const isUserExpired = useCallback((user) => {
    if (!user.expired) return false;

    try {
      const expiredDate = new Date(user.expired);
      const now = new Date();
      return expiredDate < now;
    } catch (error) {
      return false;
    }
  }, []);

  return (
    <Page title={t("installer.dashboard_title")}>
      <StyledContainer>
        <Layout />

        <Typography variant="h4" component="h1" gutterBottom>
          {t("installer.dashboard_title")}
        </Typography>

        {/* Quick Actions */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              {t("installer.quick_actions")}
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<PersonAddIcon />}
                  onClick={() => setOpenAddUserDialog(true)}
                >
                  {t("installer.add_user")}
                </Button>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<LockResetIcon />}
                  onClick={() => setOpenResetPinDialog(true)}
                >
                  {t("installer.reset_pin")}
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Search Section */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              {t("installer.search_users")}
            </Typography>
            <Grid container spacing={2} alignItems="flex-end">
              <Grid item xs={12} sm="auto" sx={{ flexGrow: 1 }}>
                <TextField
                  fullWidth
                  variant="outlined"
                  label={t("installer.search_placeholder")}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleSearch();
                    }
                  }}
                  disabled={isSearching}
                />
              </Grid>
              <Grid item>
                <MobileActionButton
                  variant="contained"
                  color="primary"
                  onClick={handleSearch}
                  startIcon={isSearching ? <CircularProgress size={16} /> : <SearchIcon />}
                  disabled={isSearching || !searchQuery.trim()}
                >
                  {isSearching ? t("installer.searching") : t("installer.search_button")}
                </MobileActionButton>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Search Results */}
        {searchResponse && searchResponse.users?.length > 0 && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {t("installer.search_results")} ({searchResponse.users.length})
              </Typography>
              {searchResponse.users.map((user, index) => {
                const lastPayload = searchResponse.lastPayload && searchResponse.lastPayload[index];
                const simData = parseSimData(searchResponse.lastSimcardLog && searchResponse.lastSimcardLog[index]);
                const expired = isUserExpired(user);

                return (
                <Card key={user._id} variant="outlined" sx={{ mb: 2 }}>
                  <CardContent>
                    {/* Header with phone number and status */}
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="h6" component="div">
                        {user.phoneNumber}
                      </Typography>
                      <Chip
                        label={expired ? t("installer.expired") : t("installer.active")}
                        color={expired ? "error" : "success"}
                        size="small"
                      />
                    </Box>

                    {/* Information Grid */}
                    <Grid container spacing={2} sx={{ mb: 2 }}>
                      {/* Device Information */}
                      <Grid item xs={12} sm={6} md={4}>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          {t("installer.device_label")}
                        </Typography>
                        <Typography variant="body2">
                          {user.devices?.deviceNumber || t("installer.no_device_assigned")}
                        </Typography>
                        {lastPayload?.lastPayload?.ver && (
                          <Typography variant="caption" color="text.secondary">
                            {t("installer.version")}: {lastPayload.lastPayload.ver}
                          </Typography>
                        )}
                      </Grid>

                      {/* Last Payload */}
                      <Grid item xs={12} sm={6} md={4}>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          {t("installer.last_payload")}
                        </Typography>
                        <Typography variant="body2">
                          {lastPayload?.lastPayloadCreatedAt ? formatDate(lastPayload.lastPayloadCreatedAt) : t("installer.no_data")}
                        </Typography>
                      </Grid>

                      {/* Expiration Date */}
                      <Grid item xs={12} sm={6} md={4}>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          {t("installer.expiration_date")}
                        </Typography>
                        <Typography variant="body2" color={expired ? "error.main" : "text.primary"}>
                          {user.expired ? formatDate(user.expired) : t("installer.no_data")}
                        </Typography>
                      </Grid>

                      {/* SIM Information - Full width for better display */}
                      <Grid item xs={12}>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          {t("installer.sim_info")}
                        </Typography>
                        <Typography variant="body2">
                          {simData ? (
                            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                              {simData.date && <span>{simData.date}</span>}
                              {simData.balance && <span>{simData.balance} MNT</span>}
                              {!simData.date && !simData.balance && t("installer.no_data")}
                            </Box>
                          ) : t("installer.no_data")}
                        </Typography>
                      </Grid>
                    </Grid>

                    {/* Action Buttons */}
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                          {!user.devices?.deviceNumber ? (
                            <Button
                              size="small"
                              variant="outlined"
                              color="success"
                              startIcon={<DevicesIcon />}
                              onClick={() => {
                                setSelectedUser(user);
                                setOpenAddDeviceDialog(true);
                              }}
                            >
                              {t("installer.add_device")}
                            </Button>
                          ) : (
                            <Button
                              size="small"
                              variant="outlined"
                              color="error"
                              onClick={() => {
                                setSelectedDeviceNumber(user.devices.deviceNumber);
                                setOpenDeleteDeviceDialog(true);
                              }}
                            >
                              {t("installer.delete_device")}
                            </Button>
                          )}
                          <Button
                            size="small"
                            variant="outlined"
                            color="warning"
                            startIcon={<LockResetIcon />}
                            onClick={() => {
                              setResetPinPhone(user.phoneNumber);
                              setOpenResetPinDialog(true);
                            }}
                          >
                            {t("installer.reset_pin")}
                          </Button>
                        </Box>
                  </CardContent>
                </Card>
                );
              })}
            </CardContent>
          </Card>
        )}

        {/* Add User Dialog */}
        <Dialog
          open={openAddUserDialog}
          onClose={() => {
            if (!isAddingUser) {
              setOpenAddUserDialog(false);
              setNewUserPhone("");
            }
          }}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>{t("installer.add_new_user")}</DialogTitle>
          <DialogContent>
            <TextField
              autoFocus
              margin="dense"
              label={t("installer.phone_number")}
              type="text"
              fullWidth
              variant="outlined"
              value={newUserPhone}
              onChange={(e) => setNewUserPhone(e.target.value)}
              required
              disabled={isAddingUser}
              placeholder={t("installer.phone_placeholder")}
            />
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => {
                setOpenAddUserDialog(false);
                setNewUserPhone("");
              }}
              disabled={isAddingUser}
            >
              {t("installer.cancel")}
            </Button>
            <Button
              onClick={handleAddUser}
              color="primary"
              variant="contained"
              disabled={isAddingUser || !newUserPhone.trim()}
              startIcon={isAddingUser ? <CircularProgress size={16} /> : null}
            >
              {isAddingUser ? t("installer.adding") : t("installer.add_user")}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Add Device Dialog */}
        <Dialog
          open={openAddDeviceDialog}
          onClose={() => {
            if (!isAddingDevice) {
              setOpenAddDeviceDialog(false);
              setNewDeviceNumber("");
            }
          }}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            {t("installer.add_device_for")} {selectedUser?.phoneNumber}
          </DialogTitle>
          <DialogContent>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {t("installer.device_default_info")}
            </Typography>
            <TextField
              autoFocus
              margin="dense"
              label={t("installer.device_number")}
              type="text"
              fullWidth
              variant="outlined"
              value={newDeviceNumber}
              onChange={(e) => setNewDeviceNumber(e.target.value)}
              required
              disabled={isAddingDevice}
              placeholder={t("installer.device_placeholder")}
            />
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => {
                setOpenAddDeviceDialog(false);
                setNewDeviceNumber("");
              }}
              disabled={isAddingDevice}
            >
              {t("installer.cancel")}
            </Button>
            <Button
              onClick={handleAddDevice}
              color="success"
              variant="contained"
              disabled={isAddingDevice || !newDeviceNumber.trim()}
              startIcon={isAddingDevice ? <CircularProgress size={16} /> : null}
            >
              {isAddingDevice ? t("installer.creating") : t("installer.create_device")}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Reset PIN Dialog */}
        <Dialog
          open={openResetPinDialog}
          onClose={() => {
            if (!isResettingPin) {
              setOpenResetPinDialog(false);
              setResetPinPhone("");
            }
          }}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>{t("installer.reset_user_pin")}</DialogTitle>
          <DialogContent>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {t("installer.reset_pin_info")}
            </Typography>
            <TextField
              autoFocus
              margin="dense"
              label={t("installer.phone_number")}
              type="text"
              fullWidth
              variant="outlined"
              value={resetPinPhone}
              onChange={(e) => setResetPinPhone(e.target.value)}
              required
              disabled={isResettingPin}
              placeholder={t("installer.phone_placeholder")}
            />
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => {
                setOpenResetPinDialog(false);
                setResetPinPhone("");
              }}
              disabled={isResettingPin}
            >
              {t("installer.cancel")}
            </Button>
            <Button
              onClick={handleResetPin}
              color="warning"
              variant="contained"
              disabled={isResettingPin || !resetPinPhone.trim()}
              startIcon={isResettingPin ? <CircularProgress size={16} /> : null}
            >
              {isResettingPin ? t("installer.resetting") : t("installer.reset_pin")}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Delete Device Dialog */}
        <Dialog
          open={openDeleteDeviceDialog}
          onClose={() => {
            if (!isDeletingDevice) {
              setOpenDeleteDeviceDialog(false);
              setSelectedDeviceNumber("");
            }
          }}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>{t("installer.delete_device_title")}</DialogTitle>
          <DialogContent>
            <DialogContentText>
              {t("installer.delete_device_confirm")}
            </DialogContentText>
            <Typography variant="subtitle1" sx={{ mt: 2 }}>
              {t("installer.device_number_label")}: {selectedDeviceNumber}
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => {
                setOpenDeleteDeviceDialog(false);
                setSelectedDeviceNumber("");
              }}
              disabled={isDeletingDevice}
            >
              {t("installer.cancel")}
            </Button>
            <Button
              onClick={handleDeleteDevice}
              color="error"
              variant="contained"
              disabled={isDeletingDevice || !selectedDeviceNumber}
              startIcon={isDeletingDevice ? <CircularProgress size={16} /> : null}
            >
              {isDeletingDevice ? t("installer.deleting") : t("installer.delete_device")}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Notification Snackbar */}
        <Snackbar
          open={notification.open}
          autoHideDuration={4000}
          onClose={hideNotification}
          anchorOrigin={{
            vertical: isMobile ? 'bottom' : 'top',
            horizontal: 'center'
          }}
        >
          <Alert
            onClose={hideNotification}
            severity={notification.severity}
            variant="filled"
            sx={{ width: '100%' }}
          >
            {notification.message}
          </Alert>
        </Snackbar>
      </StyledContainer>
    </Page>
  );
}
