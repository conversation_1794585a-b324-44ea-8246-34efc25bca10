import React, { useState, useRef, useEffect } from 'react';
import {
  Container,
  Typography,
  Paper,
  Box,
  Button,
  TextField,
  Alert,
  LinearProgress,
  CircularProgress,
  Grid,
  Card,
  CardContent,
  Chip
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import axios from '../../utils/axios';
import Iconify from '../../components/Iconify';

export default function PaymentTestPage() {
  const { t } = useTranslation();
  const [invoiceId, setInvoiceId] = useState('');
  const [paymentStatus, setPaymentStatus] = useState('idle');
  const [paymentProgress, setPaymentProgress] = useState(0);
  const [paymentResult, setPaymentResult] = useState(null);
  const [error, setError] = useState('');
  const paymentCheckInterval = useRef(null);
  const paymentCheckAttempts = useRef(0);
  const maxPaymentCheckAttempts = 60; // 5 minutes

  const checkPaymentStatus = async (invoiceId) => {
    try {
      const response = await axios.get(`/api/hook/payment/check/${invoiceId}`);
      console.log('Payment check response:', response.data);
      
      if (response.data.success && response.data.payment && response.data.payment.rows && response.data.payment.rows.length > 0) {
        // Payment found and confirmed
        setPaymentStatus('success');
        setPaymentProgress(100);
        setPaymentResult(response.data);
        clearPaymentCheck();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Payment check error:', error);
      setError(error.response?.data?.message || error.message);
      return false;
    }
  };

  const startPaymentCheck = (invoiceId) => {
    setPaymentStatus('checking');
    setPaymentProgress(0);
    setPaymentResult(null);
    setError('');
    paymentCheckAttempts.current = 0;
    
    paymentCheckInterval.current = setInterval(async () => {
      paymentCheckAttempts.current += 1;
      const progress = (paymentCheckAttempts.current / maxPaymentCheckAttempts) * 100;
      setPaymentProgress(Math.min(progress, 95));
      
      const paymentConfirmed = await checkPaymentStatus(invoiceId);
      
      if (paymentConfirmed) {
        return; // Payment confirmed, interval cleared
      }
      
      if (paymentCheckAttempts.current >= maxPaymentCheckAttempts) {
        // Timeout reached
        clearPaymentCheck();
        setPaymentStatus('failed');
        setError('Payment verification timed out after 5 minutes');
      }
    }, 5000); // Check every 5 seconds
  };

  const clearPaymentCheck = () => {
    if (paymentCheckInterval.current) {
      clearInterval(paymentCheckInterval.current);
      paymentCheckInterval.current = null;
    }
  };

  const handleManualCheck = async () => {
    if (!invoiceId.trim()) {
      setError('Please enter an invoice ID');
      return;
    }

    setPaymentStatus('checking');
    setPaymentProgress(50);
    setError('');
    
    const paymentConfirmed = await checkPaymentStatus(invoiceId);
    if (!paymentConfirmed) {
      setPaymentStatus('failed');
      setError('Payment not found or not yet confirmed');
    }
  };

  const handleStartAutoCheck = () => {
    if (!invoiceId.trim()) {
      setError('Please enter an invoice ID');
      return;
    }
    startPaymentCheck(invoiceId);
  };

  const handleStopCheck = () => {
    clearPaymentCheck();
    setPaymentStatus('idle');
    setPaymentProgress(0);
  };

  const handleReset = () => {
    clearPaymentCheck();
    setPaymentStatus('idle');
    setPaymentProgress(0);
    setPaymentResult(null);
    setError('');
    setInvoiceId('');
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearPaymentCheck();
    };
  }, []);

  const getStatusColor = () => {
    switch (paymentStatus) {
      case 'checking': return 'info';
      case 'success': return 'success';
      case 'failed': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = () => {
    switch (paymentStatus) {
      case 'checking': return 'eva:clock-outline';
      case 'success': return 'eva:checkmark-circle-2-fill';
      case 'failed': return 'eva:close-circle-fill';
      default: return 'eva:question-mark-circle-outline';
    }
  };

  return (
    <Container maxWidth="md">
      <Box sx={{ py: 3 }}>
        <Typography variant="h4" gutterBottom>
          Payment Verification Test
        </Typography>
        <Typography variant="body1" color="text.secondary" gutterBottom>
          Test the automatic payment verification functionality
        </Typography>

        <Paper sx={{ p: 3, mt: 3 }}>
          <Grid container spacing={3}>
            {/* Input Section */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Invoice ID"
                value={invoiceId}
                onChange={(e) => setInvoiceId(e.target.value)}
                placeholder="Enter invoice ID to check"
                disabled={paymentStatus === 'checking'}
              />
            </Grid>

            {/* Control Buttons */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Button
                  variant="contained"
                  onClick={handleStartAutoCheck}
                  disabled={paymentStatus === 'checking' || !invoiceId.trim()}
                  startIcon={<Iconify icon="eva:play-circle-outline" />}
                >
                  Start Auto Check
                </Button>
                
                <Button
                  variant="outlined"
                  onClick={handleManualCheck}
                  disabled={paymentStatus === 'checking' || !invoiceId.trim()}
                  startIcon={<Iconify icon="eva:search-outline" />}
                >
                  Manual Check
                </Button>
                
                <Button
                  variant="outlined"
                  color="warning"
                  onClick={handleStopCheck}
                  disabled={paymentStatus !== 'checking'}
                  startIcon={<Iconify icon="eva:stop-circle-outline" />}
                >
                  Stop Check
                </Button>
                
                <Button
                  variant="outlined"
                  onClick={handleReset}
                  startIcon={<Iconify icon="eva:refresh-outline" />}
                >
                  Reset
                </Button>
              </Box>
            </Grid>

            {/* Status Display */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                    <Iconify icon={getStatusIcon()} sx={{ fontSize: 24 }} />
                    <Typography variant="h6">
                      Payment Status
                    </Typography>
                    <Chip 
                      label={paymentStatus.toUpperCase()} 
                      color={getStatusColor()}
                      size="small"
                    />
                  </Box>

                  {paymentStatus === 'checking' && (
                    <Box sx={{ mb: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                        <CircularProgress size={20} />
                        <Typography variant="body2">
                          Checking payment... Attempt {paymentCheckAttempts.current} of {maxPaymentCheckAttempts}
                        </Typography>
                      </Box>
                      <LinearProgress 
                        variant="determinate" 
                        value={paymentProgress} 
                        sx={{ mb: 1 }}
                      />
                      <Typography variant="caption" color="text.secondary">
                        Progress: {Math.round(paymentProgress)}%
                      </Typography>
                    </Box>
                  )}

                  {error && (
                    <Alert severity="error" sx={{ mb: 2 }}>
                      {error}
                    </Alert>
                  )}

                  {paymentStatus === 'success' && (
                    <Alert severity="success" sx={{ mb: 2 }}>
                      Payment confirmed successfully!
                    </Alert>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* Payment Result */}
            {paymentResult && (
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Payment Details
                    </Typography>
                    <Box sx={{ bgcolor: 'grey.50', p: 2, borderRadius: 1 }}>
                      <pre style={{ margin: 0, fontSize: '12px', overflow: 'auto' }}>
                        {JSON.stringify(paymentResult, null, 2)}
                      </pre>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            )}

            {/* Instructions */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    How to Test
                  </Typography>
                  <Typography variant="body2" component="div">
                    <ol>
                      <li>Enter a valid invoice ID from a recent payment</li>
                      <li>Click "Start Auto Check" to begin automatic verification</li>
                      <li>The system will check every 5 seconds for up to 5 minutes</li>
                      <li>Use "Manual Check" for one-time verification</li>
                      <li>Payment status will update automatically when confirmed</li>
                    </ol>
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Paper>
      </Box>
    </Container>
  );
}
