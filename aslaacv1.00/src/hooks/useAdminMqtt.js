import { useState, useEffect, useCallback, useRef } from 'react';
import adminMqttService from '../services/adminMqttService';

export const useAdminMqtt = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [error, setError] = useState(null);
  const [realtimeData, setRealtimeData] = useState([]);
  const [statisticsUpdates, setStatisticsUpdates] = useState(null);
  const connectionAttempted = useRef(false);
  const cleanupRef = useRef(null);

  // Update connection status
  const updateStatus = useCallback(() => {
    const status = adminMqttService.getStatus();
    setIsConnected(status.isConnected);
    setConnectionStatus(status.isConnected ? 'connected' : 'disconnected');
  }, []);

  // Connect to MQTT
  const connect = useCallback(async () => {
    if (connectionAttempted.current) {
      return;
    }

    connectionAttempted.current = true;
    setConnectionStatus('connecting');
    setError(null);

    try {
      await adminMqttService.connect();
      setIsConnected(true);
      setConnectionStatus('connected');
      console.log('Admin MQTT Hook: Connected successfully');
    } catch (error) {
      console.error('Admin MQTT Hook: Connection failed:', error);
      setError(error.message);
      setConnectionStatus('error');
      setIsConnected(false);
      connectionAttempted.current = false; // Allow retry
    }
  }, []);

  // Disconnect from MQTT
  const disconnect = useCallback(() => {
    adminMqttService.disconnect();
    setIsConnected(false);
    setConnectionStatus('disconnected');
    setError(null);
    connectionAttempted.current = false;
    
    if (cleanupRef.current) {
      cleanupRef.current();
      cleanupRef.current = null;
    }
  }, []);

  // Subscribe to a topic
  const subscribe = useCallback((topic, callback) => {
    if (isConnected) {
      adminMqttService.subscribe(topic, callback);
    }
  }, [isConnected]);

  // Unsubscribe from a topic
  const unsubscribe = useCallback((topic, callback) => {
    adminMqttService.unsubscribe(topic, callback);
  }, []);

  // Publish statistics refresh
  const refreshStatistics = useCallback(() => {
    adminMqttService.publishStatisticsRefresh();
  }, []);

  // Handle real-time statistics updates
  useEffect(() => {
    if (!isConnected) return;

    const handleStatisticsUpdate = (data) => {
      console.log('Admin MQTT Hook: Received statistics update:', data);
      
      if (data.type === 'device_response') {
        // Add to real-time data feed
        setRealtimeData(prev => {
          const newData = [...prev, data];
          // Keep only last 100 entries
          return newData.slice(-100);
        });
      } else if (data.type === 'statistics_update') {
        // Update statistics
        setStatisticsUpdates(data);
      }
    };

    // Subscribe to statistics updates
    const unsubscribeStats = adminMqttService.onStatisticsUpdate(handleStatisticsUpdate);
    cleanupRef.current = unsubscribeStats;

    return () => {
      if (unsubscribeStats) {
        unsubscribeStats();
      }
    };
  }, [isConnected]);

  // Auto-connect on mount
  useEffect(() => {
    connect();

    // Cleanup on unmount
    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  // Periodic status update
  useEffect(() => {
    const interval = setInterval(updateStatus, 5000);
    return () => clearInterval(interval);
  }, [updateStatus]);

  return {
    // Connection state
    isConnected,
    connectionStatus,
    error,
    
    // Connection methods
    connect,
    disconnect,
    
    // Subscription methods
    subscribe,
    unsubscribe,
    
    // Real-time data
    realtimeData,
    statisticsUpdates,
    
    // Utility methods
    refreshStatistics,
    getStatus: () => adminMqttService.getStatus(),
    
    // Clear real-time data
    clearRealtimeData: () => setRealtimeData([]),
    clearStatisticsUpdates: () => setStatisticsUpdates(null)
  };
};

export default useAdminMqtt;
