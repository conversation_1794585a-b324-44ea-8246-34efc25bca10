import React from 'react';
import PropTypes from 'prop-types';
import { Card, CardContent, Typography, Box, Grid } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Bar } from 'react-chartjs-2';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

ResponseTimeChart.propTypes = {
  data: PropTypes.object
};

export default function ResponseTimeChart({ data }) {
  const theme = useTheme();

  if (!data || !data.commandTypes) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Response Time Analytics
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
            <Typography variant="body2" color="text.secondary">
              No data available
            </Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  const { commandTypes, overview } = data;

  // Prepare data for response time by command type
  const responseTimeData = {
    labels: commandTypes.map(type => type._id || 'Unknown'),
    datasets: [
      {
        label: 'Average Response Time (ms)',
        data: commandTypes.map(type => Math.round(type.avgResponseTime || 0)),
        backgroundColor: theme.palette.primary.main,
        borderColor: theme.palette.primary.dark,
        borderWidth: 1,
      },
    ],
  };

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Average Response Time by Command Type',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Response Time (ms)'
        }
      },
      x: {
        title: {
          display: true,
          text: 'Command Type'
        }
      }
    },
  };

  // Response time categories for distribution
  const getResponseTimeCategory = (time) => {
    if (time < 1000) return 'Fast (<1s)';
    if (time < 3000) return 'Medium (1-3s)';
    if (time < 5000) return 'Slow (3-5s)';
    return 'Very Slow (>5s)';
  };

  // Calculate response time distribution
  const responseTimeDistribution = commandTypes.reduce((acc, type) => {
    const category = getResponseTimeCategory(type.avgResponseTime || 0);
    acc[category] = (acc[category] || 0) + type.count;
    return acc;
  }, {});

  const distributionData = {
    labels: Object.keys(responseTimeDistribution),
    datasets: [
      {
        label: 'Commands',
        data: Object.values(responseTimeDistribution),
        backgroundColor: [
          theme.palette.success.main,
          theme.palette.warning.main,
          theme.palette.error.light,
          theme.palette.error.main
        ],
        borderWidth: 1,
      },
    ],
  };

  const distributionOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Response Time Distribution',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Number of Commands'
        }
      }
    },
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Response Time Analytics
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Box sx={{ height: 300 }}>
              <Bar data={responseTimeData} options={chartOptions} />
            </Box>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Box sx={{ height: 300 }}>
              <Bar data={distributionData} options={distributionOptions} />
            </Box>
          </Grid>
        </Grid>
        
        <Grid container spacing={2} sx={{ mt: 2 }}>
          <Grid item xs={6} sm={3}>
            <Box sx={{ textAlign: 'center', p: 1, bgcolor: 'background.neutral', borderRadius: 1 }}>
              <Typography variant="h6" color="primary.main">
                {overview.minResponseTime ? Math.round(overview.minResponseTime) : 0}ms
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Fastest Response
              </Typography>
            </Box>
          </Grid>
          
          <Grid item xs={6} sm={3}>
            <Box sx={{ textAlign: 'center', p: 1, bgcolor: 'background.neutral', borderRadius: 1 }}>
              <Typography variant="h6" color="info.main">
                {overview.avgResponseTime ? Math.round(overview.avgResponseTime) : 0}ms
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Average Response
              </Typography>
            </Box>
          </Grid>
          
          <Grid item xs={6} sm={3}>
            <Box sx={{ textAlign: 'center', p: 1, bgcolor: 'background.neutral', borderRadius: 1 }}>
              <Typography variant="h6" color="warning.main">
                {overview.maxResponseTime ? Math.round(overview.maxResponseTime) : 0}ms
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Slowest Response
              </Typography>
            </Box>
          </Grid>
          
          <Grid item xs={6} sm={3}>
            <Box sx={{ textAlign: 'center', p: 1, bgcolor: 'background.neutral', borderRadius: 1 }}>
              <Typography variant="h6" color="success.main">
                {commandTypes.filter(type => (type.avgResponseTime || 0) < 1000).length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Fast Commands
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
}
