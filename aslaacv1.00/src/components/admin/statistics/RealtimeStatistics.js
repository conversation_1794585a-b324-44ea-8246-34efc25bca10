import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Tooltip,
  Switch,
  FormControlLabel,
  Alert,
  Badge,
  Divider
} from '@mui/material';
import {
  Wifi as WifiIcon,
  WifiOff as WifiOffIcon,
  Refresh as RefreshIcon,
  Clear as ClearIcon,
  DeviceHub as DeviceIcon,
  Schedule as ScheduleIcon,
  TrendingUp as TrendingUpIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { formatDistanceToNow } from 'date-fns';
import useAdminMqtt from '../../../hooks/useAdminMqtt';

export default function RealtimeStatistics({ onStatisticsUpdate }) {
  const { t } = useTranslation();
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [showDetails, setShowDetails] = useState(true);
  
  const {
    isConnected,
    connectionStatus,
    error,
    realtimeData,
    statisticsUpdates,
    refreshStatistics,
    clearRealtimeData,
    clearStatisticsUpdates,
    getStatus
  } = useAdminMqtt();

  // Auto-refresh statistics when updates are received
  useEffect(() => {
    if (autoRefresh && statisticsUpdates && onStatisticsUpdate) {
      console.log('Realtime: Triggering statistics refresh due to update');
      onStatisticsUpdate();
      clearStatisticsUpdates();
    }
  }, [statisticsUpdates, autoRefresh, onStatisticsUpdate, clearStatisticsUpdates]);

  // Get connection status color
  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'success';
      case 'connecting': return 'warning';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  // Get connection status text
  const getStatusText = () => {
    switch (connectionStatus) {
      case 'connected': return t('statistics.realtime.connected');
      case 'connecting': return t('statistics.realtime.connecting');
      case 'error': return t('statistics.realtime.error');
      default: return t('statistics.realtime.disconnected');
    }
  };

  // Format device response for display
  const formatDeviceResponse = (data) => {
    const { deviceNumber, response, timestamp } = data;
    const timeAgo = formatDistanceToNow(timestamp, { addSuffix: true });
    
    return {
      primary: `Device ${deviceNumber}`,
      secondary: response.length > 50 ? `${response.substring(0, 50)}...` : response,
      time: timeAgo
    };
  };

  // Get MQTT status info
  const mqttStatus = getStatus();

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <TrendingUpIcon />
            {t('statistics.realtime.title')}
          </Typography>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Chip
              icon={isConnected ? <WifiIcon /> : <WifiOffIcon />}
              label={getStatusText()}
              color={getStatusColor()}
              size="small"
            />
            
            <Tooltip title={t('statistics.realtime.refresh')}>
              <IconButton 
                size="small" 
                onClick={refreshStatistics}
                disabled={!isConnected}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>
            
            <Tooltip title={t('statistics.realtime.clear')}>
              <IconButton 
                size="small" 
                onClick={clearRealtimeData}
              >
                <ClearIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Connection Error */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {t('statistics.realtime.connection_error')}: {error}
          </Alert>
        )}

        {/* Settings */}
        <Box sx={{ mb: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                size="small"
              />
            }
            label={t('statistics.realtime.auto_refresh')}
          />
          
          <FormControlLabel
            control={
              <Switch
                checked={showDetails}
                onChange={(e) => setShowDetails(e.target.checked)}
                size="small"
              />
            }
            label={t('statistics.realtime.show_details')}
            sx={{ ml: 2 }}
          />
        </Box>

        {/* Connection Details */}
        {showDetails && (
          <Box sx={{ mb: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
            <Typography variant="subtitle2" gutterBottom>
              {t('statistics.realtime.connection_details')}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {t('statistics.realtime.client_id')}: {mqttStatus.clientId}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {t('statistics.realtime.subscribers')}: {mqttStatus.subscriberCount}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {t('statistics.realtime.callbacks')}: {mqttStatus.statisticsCallbackCount}
            </Typography>
          </Box>
        )}

        <Divider sx={{ my: 2 }} />

        {/* Real-time Data Feed */}
        <Box>
          <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
            <DeviceIcon />
            {t('statistics.realtime.live_feed')}
            {realtimeData.length > 0 && (
              <Badge badgeContent={realtimeData.length} color="primary" />
            )}
          </Typography>

          {realtimeData.length === 0 ? (
            <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 2 }}>
              {isConnected 
                ? t('statistics.realtime.waiting_for_data')
                : t('statistics.realtime.connect_to_see_data')
              }
            </Typography>
          ) : (
            <List dense sx={{ maxHeight: 300, overflow: 'auto' }}>
              {realtimeData.slice().reverse().map((data, index) => {
                const formatted = formatDeviceResponse(data);
                return (
                  <ListItem key={`${data.timestamp}-${index}`} divider>
                    <ListItemIcon>
                      <DeviceIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary={formatted.primary}
                      secondary={
                        <Box>
                          <Typography variant="body2" component="div">
                            {formatted.secondary}
                          </Typography>
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mt: 0.5 }}>
                            <ScheduleIcon sx={{ fontSize: 12 }} />
                            {formatted.time}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                );
              })}
            </List>
          )}
        </Box>
      </CardContent>
    </Card>
  );
}

RealtimeStatistics.propTypes = {
  onStatisticsUpdate: PropTypes.func
};
