import React from 'react';
import PropTypes from 'prop-types';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  LinearProgress
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Line, Bar } from 'react-chartjs-2';
import { formatDistanceToNow } from 'date-fns';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
);

DevicePerformanceChart.propTypes = {
  data: PropTypes.object,
  onDeviceClick: PropTypes.func,
  filters: PropTypes.object
};

export default function DevicePerformanceChart({ data, onDeviceClick, filters = {} }) {
  const theme = useTheme();

  const handleDeviceClick = (deviceNumber) => {
    if (onDeviceClick) {
      onDeviceClick(deviceNumber);
    }
  };

  if (!data || !data.devices) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Device Performance Analytics
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
            <Typography variant="body2" color="text.secondary">
              No device data available
            </Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  const { devices, responseTimeDistribution } = data;

  // Prepare data for device response times
  const deviceResponseData = {
    labels: devices.slice(0, 10).map(device => device._id || 'Unknown'),
    datasets: [
      {
        label: 'Average Response Time (ms)',
        data: devices.slice(0, 10).map(device => Math.round(device.avgResponseTime || 0)),
        backgroundColor: devices.slice(0, 10).map(device => {
          const time = device.avgResponseTime || 0;
          if (time < 1000) return theme.palette.success.main;
          if (time < 3000) return theme.palette.warning.main;
          return theme.palette.error.main;
        }),
        borderWidth: 1,
      },
    ],
  };

  // Prepare data for success rates
  const successRateData = {
    labels: devices.slice(0, 10).map(device => device._id || 'Unknown'),
    datasets: [
      {
        label: 'Success Rate (%)',
        data: devices.slice(0, 10).map(device => Math.round(device.successRate || 0)),
        borderColor: theme.palette.primary.main,
        backgroundColor: theme.palette.primary.light,
        tension: 0.1,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  const getPerformanceCategory = (device) => {
    const successRate = device.successRate || 0;
    const responseTime = device.avgResponseTime || 0;
    
    if (successRate >= 95 && responseTime < 1000) return { label: 'Excellent', color: 'success' };
    if (successRate >= 85 && responseTime < 3000) return { label: 'Good', color: 'info' };
    if (successRate >= 70 && responseTime < 5000) return { label: 'Fair', color: 'warning' };
    return { label: 'Poor', color: 'error' };
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Device Performance Analytics
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Box sx={{ height: 300 }}>
              <Typography variant="subtitle2" gutterBottom>
                Response Time by Device
              </Typography>
              <Bar data={deviceResponseData} options={chartOptions} />
            </Box>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Box sx={{ height: 300 }}>
              <Typography variant="subtitle2" gutterBottom>
                Success Rate by Device
              </Typography>
              <Line data={successRateData} options={chartOptions} />
            </Box>
          </Grid>
        </Grid>
        
        <Box sx={{ mt: 3 }}>
          <Typography variant="h6" gutterBottom>
            Device Performance Summary
          </Typography>
          
          <TableContainer component={Paper} variant="outlined">
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Device</TableCell>
                  <TableCell align="center">Commands</TableCell>
                  <TableCell align="center">Success Rate</TableCell>
                  <TableCell align="center">Avg Response</TableCell>
                  <TableCell align="center">Performance</TableCell>
                  <TableCell align="center">Last Activity</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {devices.slice(0, 15).map((device, index) => {
                  const performance = getPerformanceCategory(device);
                  return (
                    <TableRow
                      key={device._id || index}
                      hover
                      onClick={() => handleDeviceClick(device._id)}
                      sx={{ cursor: onDeviceClick ? 'pointer' : 'default' }}
                    >
                      <TableCell>
                        <Box>
                          <Typography variant="subtitle2">
                            {device._id || 'Unknown'}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {device.deviceType || '4g'}
                          </Typography>
                        </Box>
                      </TableCell>
                      
                      <TableCell align="center">
                        <Typography variant="body2">
                          {device.totalCommands || 0}
                        </Typography>
                      </TableCell>
                      
                      <TableCell align="center">
                        <Box sx={{ minWidth: 80 }}>
                          <LinearProgress
                            variant="determinate"
                            value={device.successRate || 0}
                            sx={{ 
                              mb: 0.5,
                              height: 6,
                              borderRadius: 3,
                              bgcolor: 'grey.200',
                              '& .MuiLinearProgress-bar': {
                                bgcolor: performance.color === 'success' 
                                  ? theme.palette.success.main 
                                  : performance.color === 'warning'
                                  ? theme.palette.warning.main
                                  : theme.palette.error.main
                              }
                            }}
                          />
                          <Typography variant="body2">
                            {Math.round(device.successRate || 0)}%
                          </Typography>
                        </Box>
                      </TableCell>
                      
                      <TableCell align="center">
                        <Typography variant="body2">
                          {Math.round(device.avgResponseTime || 0)}ms
                        </Typography>
                      </TableCell>
                      
                      <TableCell align="center">
                        <Chip
                          label={performance.label}
                          size="small"
                          color={performance.color}
                          variant="outlined"
                        />
                      </TableCell>
                      
                      <TableCell align="center">
                        <Typography variant="body2" color="text.secondary">
                          {device.lastActivity 
                            ? formatDistanceToNow(new Date(device.lastActivity), { addSuffix: true })
                            : 'Never'
                          }
                        </Typography>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      </CardContent>
    </Card>
  );
}
