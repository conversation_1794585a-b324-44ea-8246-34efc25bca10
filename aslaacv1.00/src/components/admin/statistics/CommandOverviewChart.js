import React from 'react';
import PropTypes from 'prop-types';
import { Card, CardContent, Typography, Box, Grid, Divider } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';
import { Pie, Doughnut } from 'react-chartjs-2';
import { useTranslation } from 'react-i18next';

// Register ChartJS components
ChartJS.register(ArcElement, Tooltip, Legend);

CommandOverviewChart.propTypes = {
  data: PropTypes.object,
  detailed: PropTypes.bool
};

export default function CommandOverviewChart({ data, detailed = false }) {
  const theme = useTheme();
  const { t } = useTranslation();

  if (!data || !data.overview) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Command Statistics
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
            <Typography variant="body2" color="text.secondary">
              No data available
            </Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  const { overview, commandTypes, failureReasons } = data;
  
  // Success/Failure chart data
  const successFailureData = {
    labels: ['Successful', 'Failed'],
    datasets: [
      {
        data: [overview.successfulCommands || 0, overview.failedCommands || 0],
        backgroundColor: [
          theme.palette.success.main,
          theme.palette.error.main
        ],
        borderColor: [
          theme.palette.success.dark,
          theme.palette.error.dark
        ],
        borderWidth: 1,
      },
    ],
  };

  // Command types chart data
  const commandTypesData = {
    labels: commandTypes?.map(type => type._id) || [],
    datasets: [
      {
        data: commandTypes?.map(type => type.count) || [],
        backgroundColor: [
          theme.palette.primary.main,
          theme.palette.secondary.main,
          theme.palette.info.main,
          theme.palette.warning.main,
          theme.palette.error.main,
          theme.palette.success.main,
          '#9c27b0',
          '#795548'
        ],
        borderWidth: 1,
      },
    ],
  };

  // Failure reasons chart data
  const failureReasonsData = {
    labels: failureReasons?.map(reason => reason._id) || [],
    datasets: [
      {
        data: failureReasons?.map(reason => reason.count) || [],
        backgroundColor: [
          theme.palette.error.light,
          theme.palette.error.main,
          theme.palette.error.dark,
          '#ff9800',
          '#ff5722'
        ],
        borderWidth: 1,
      },
    ],
  };

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
      },
    },
  };

  // Calculate success rate
  const totalCommands = overview.totalCommands || 0;
  const successRate = totalCommands > 0 
    ? ((overview.successfulCommands / totalCommands) * 100).toFixed(1) 
    : 0;

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Command Statistics
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={detailed ? 4 : 6}>
            <Box sx={{ height: 240, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <Typography variant="subtitle2" gutterBottom>
                Success/Failure Rate
              </Typography>
              <Box sx={{ height: 200, width: '100%' }}>
                <Doughnut data={successFailureData} options={chartOptions} />
              </Box>
            </Box>
          </Grid>
          
          {detailed && (
            <Grid item xs={12} md={4}>
              <Box sx={{ height: 240, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                <Typography variant="subtitle2" gutterBottom>
                  Command Types
                </Typography>
                <Box sx={{ height: 200, width: '100%' }}>
                  <Pie data={commandTypesData} options={chartOptions} />
                </Box>
              </Box>
            </Grid>
          )}
          
          <Grid item xs={12} md={detailed ? 4 : 6}>
            <Box sx={{ height: 240, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <Typography variant="subtitle2" gutterBottom>
                {detailed ? 'Failure Reasons' : 'Command Types'}
              </Typography>
              <Box sx={{ height: 200, width: '100%' }}>
                {detailed ? (
                  <Pie data={failureReasonsData} options={chartOptions} />
                ) : (
                  <Pie data={commandTypesData} options={chartOptions} />
                )}
              </Box>
            </Box>
          </Grid>
        </Grid>
        
        <Divider sx={{ my: 2 }} />
        
        <Grid container spacing={2}>
          <Grid item xs={6} sm={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="primary.main">
                {totalCommands}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {t('statistics.charts.command_overview.total_commands')}
              </Typography>
            </Box>
          </Grid>
          
          <Grid item xs={6} sm={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="success.main">
                {successRate}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {t('statistics.charts.command_overview.success_rate')}
              </Typography>
            </Box>
          </Grid>
          
          <Grid item xs={6} sm={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="info.main">
                {overview.avgResponseTime ? Math.round(overview.avgResponseTime) : 0}ms
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Avg Response Time
              </Typography>
            </Box>
          </Grid>
          
          <Grid item xs={6} sm={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="warning.main">
                {commandTypes?.length || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Command Types
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
}
