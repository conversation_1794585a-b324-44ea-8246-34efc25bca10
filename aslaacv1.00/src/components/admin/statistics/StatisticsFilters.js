import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Paper,
  Grid,
  TextField,
  MenuItem,
  Button,
  IconButton,
  Tooltip,
  Collapse,
  Typography
} from '@mui/material';
// Using native HTML date inputs for better compatibility
import FilterListIcon from '@mui/icons-material/FilterList';
import RefreshIcon from '@mui/icons-material/Refresh';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import SimpleDatePicker from './SimpleDatePicker';

StatisticsFilters.propTypes = {
  filters: PropTypes.object.isRequired,
  onFilterChange: PropTypes.func.isRequired,
  onRefresh: PropTypes.func.isRequired,
  loading: PropTypes.bool
};

export default function StatisticsFilters({ filters, onFilterChange, onRefresh, loading }) {
  const { t } = useTranslation();
  const [expanded, setExpanded] = useState(false);
  const [localFilters, setLocalFilters] = useState(filters);

  const handleExpandClick = () => {
    setExpanded(!expanded);
  };

  const handleDateChange = (field, date) => {
    setLocalFilters({
      ...localFilters,
      [field]: date
    });
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setLocalFilters({
      ...localFilters,
      [name]: value
    });
  };

  const handleApplyFilters = () => {
    onFilterChange(localFilters);
  };

  const handleResetFilters = () => {
    const resetFilters = {
      startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
      endDate: new Date(),
      period: 'daily',
      deviceNumber: '',
      userId: '',
      commandType: ''
    };
    setLocalFilters(resetFilters);
    onFilterChange(resetFilters);
  };

  const periodOptions = [
    { value: 'daily', label: t('statistics.filters.periods.day') },
    { value: 'weekly', label: t('statistics.filters.periods.week') },
    { value: 'monthly', label: t('statistics.filters.periods.month') }
  ];

  const commandTypeOptions = [
    { value: '', label: 'All Commands' },
    { value: 'power_on', label: 'Power On' },
    { value: 'power_off', label: 'Power Off' },
    { value: 'lock', label: 'Lock' },
    { value: 'unlock', label: 'Unlock' },
    { value: 'location', label: 'Location' },
    { value: 'status', label: 'Status' },
    { value: 'config', label: 'Configuration' },
    { value: 'other', label: 'Other' }
  ];

  return (
    <Paper sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <FilterListIcon sx={{ mr: 1 }} />
          <Typography variant="subtitle1">{t('statistics.filters.title')}</Typography>
        </Box>
        
        <Box>
          <Tooltip title="Refresh Data">
            <IconButton onClick={onRefresh} disabled={loading}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title={expanded ? "Collapse Filters" : "Expand Filters"}>
            <IconButton onClick={handleExpandClick}>
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </Tooltip>
        </Box>
      </Box>
      
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={3}>
            <SimpleDatePicker
              label={t('statistics.filters.start_date')}
              value={localFilters.startDate}
              onChange={(date) => handleDateChange('startDate', date)}
              fullWidth
              size="small"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <SimpleDatePicker
              label={t('statistics.filters.end_date')}
              value={localFilters.endDate}
              onChange={(date) => handleDateChange('endDate', date)}
              fullWidth
              size="small"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <TextField
              select
              label={t('statistics.filters.period')}
              name="period"
              value={localFilters.period}
              onChange={handleInputChange}
              fullWidth
              size="small"
            >
              {periodOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button 
                variant="contained" 
                color="primary" 
                onClick={handleApplyFilters}
                disabled={loading}
              >
                {t('statistics.filters.apply_filters')}
              </Button>
              <Button
                variant="outlined"
                onClick={handleResetFilters}
                disabled={loading}
              >
                {t('statistics.filters.reset_filters')}
              </Button>
            </Box>
          </Grid>
        </Grid>

        <Collapse in={expanded} timeout="auto" unmountOnExit>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={4}>
                <TextField
                  label={t('statistics.filters.device_number')}
                  name="deviceNumber"
                  value={localFilters.deviceNumber}
                  onChange={handleInputChange}
                  fullWidth
                  size="small"
                  placeholder="Filter by device number"
                />
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <TextField
                  label={t('statistics.filters.user_id')}
                  name="userId"
                  value={localFilters.userId}
                  onChange={handleInputChange}
                  fullWidth
                  size="small"
                  placeholder="Filter by user ID"
                />
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <TextField
                  select
                  label={t('statistics.filters.command_type')}
                  name="commandType"
                  value={localFilters.commandType}
                  onChange={handleInputChange}
                  fullWidth
                  size="small"
                >
                  {commandTypeOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>
            </Grid>
          </Box>
        </Collapse>
    </Paper>
  );
}
