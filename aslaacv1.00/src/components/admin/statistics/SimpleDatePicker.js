import React from 'react';
import PropTypes from 'prop-types';
import { TextField, InputAdornment, IconButton } from '@mui/material';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';

SimpleDatePicker.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.instanceOf(Date),
  onChange: PropTypes.func.isRequired,
  fullWidth: PropTypes.bool,
  size: PropTypes.string,
  disabled: PropTypes.bool
};

export default function SimpleDatePicker({ 
  label, 
  value, 
  onChange, 
  fullWidth = false, 
  size = "medium",
  disabled = false 
}) {
  
  const handleDateChange = (event) => {
    const dateString = event.target.value;
    if (dateString) {
      const date = new Date(dateString);
      onChange(date);
    } else {
      onChange(null);
    }
  };

  const formatDateForInput = (date) => {
    if (!date) return '';
    
    // Format date as YYYY-MM-DD for HTML date input
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  };

  return (
    <TextField
      label={label}
      type="date"
      value={formatDateForInput(value)}
      onChange={handleDateChange}
      fullWidth={fullWidth}
      size={size}
      disabled={disabled}
      InputLabelProps={{
        shrink: true,
      }}
      InputProps={{
        endAdornment: (
          <InputAdornment position="end">
            <IconButton edge="end" disabled>
              <CalendarTodayIcon />
            </IconButton>
          </InputAdornment>
        ),
      }}
      sx={{
        '& input[type="date"]::-webkit-calendar-picker-indicator': {
          opacity: 0,
          position: 'absolute',
          right: 0,
          width: '100%',
          height: '100%',
          cursor: 'pointer'
        }
      }}
    />
  );
}
