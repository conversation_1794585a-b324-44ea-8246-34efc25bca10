import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  Button,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  CircularProgress
} from '@mui/material';
import GetAppIcon from '@mui/icons-material/GetApp';
import TableChartIcon from '@mui/icons-material/TableChart';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import ImageIcon from '@mui/icons-material/Image';
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
import axios from '../../../utils/axios';

ExportButton.propTypes = {
  filters: PropTypes.object.isRequired,
  onExport: PropTypes.func
};

export default function ExportButton({ filters, onExport }) {
  const [anchorEl, setAnchorEl] = useState(null);
  const [loading, setLoading] = useState(false);
  const open = Boolean(anchorEl);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const exportToExcel = async () => {
    setLoading(true);
    try {
      // Fetch detailed logs for export
      const params = {
        startDate: filters.startDate.toISOString(),
        endDate: filters.endDate.toISOString(),
        limit: 10000, // Large limit for export
        ...(filters.deviceNumber && { deviceNumber: filters.deviceNumber }),
        ...(filters.userId && { userId: filters.userId }),
        ...(filters.commandType && { commandType: filters.commandType })
      };

      const response = await axios.get('/api/admin/statistics/detailed-logs', { params });
      
      if (response.data.success) {
        const logs = response.data.data.logs;
        
        // Prepare data for Excel
        const excelData = logs.map(log => ({
          'Date': new Date(log.createdAt).toLocaleString(),
          'User': log.user || 'Unknown',
          'Device Number': log.deviceNumber,
          'Device Type': log.deviceType,
          'Command': log.command,
          'Command Type': log.commandType,
          'Status': log.responseStatus,
          'Success': log.success ? 'Yes' : 'No',
          'Response Time (ms)': log.responseTime || 0,
          'Sent Time': log.sentTime ? new Date(log.sentTime).toLocaleString() : '',
          'Receive Time': log.receiveTime ? new Date(log.receiveTime).toLocaleString() : '',
          'Response': log.response,
          'Failure Reason': log.failureReason || '',
          'Device Online': log.deviceOnline ? 'Yes' : 'No',
          'Signal Strength': log.signalStrength || ''
        }));

        // Create workbook and worksheet
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(excelData);
        
        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, 'Command Statistics');
        
        // Generate Excel file
        const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
        const data = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        
        // Save file
        const fileName = `command-statistics-${filters.startDate.toISOString().split('T')[0]}-to-${filters.endDate.toISOString().split('T')[0]}.xlsx`;
        saveAs(data, fileName);
      }
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      alert('Failed to export data to Excel');
    } finally {
      setLoading(false);
      handleClose();
    }
  };

  const exportToCSV = async () => {
    setLoading(true);
    try {
      // Fetch detailed logs for export
      const params = {
        startDate: filters.startDate.toISOString(),
        endDate: filters.endDate.toISOString(),
        limit: 10000, // Large limit for export
        ...(filters.deviceNumber && { deviceNumber: filters.deviceNumber }),
        ...(filters.userId && { userId: filters.userId }),
        ...(filters.commandType && { commandType: filters.commandType })
      };

      const response = await axios.get('/api/admin/statistics/detailed-logs', { params });
      
      if (response.data.success) {
        const logs = response.data.data.logs;
        
        // Prepare CSV data
        const csvHeaders = [
          'Date',
          'User',
          'Device Number',
          'Device Type',
          'Command',
          'Command Type',
          'Status',
          'Success',
          'Response Time (ms)',
          'Sent Time',
          'Receive Time',
          'Response',
          'Failure Reason',
          'Device Online',
          'Signal Strength'
        ];

        const csvData = logs.map(log => [
          new Date(log.createdAt).toLocaleString(),
          log.user || 'Unknown',
          log.deviceNumber,
          log.deviceType,
          log.command,
          log.commandType,
          log.responseStatus,
          log.success ? 'Yes' : 'No',
          log.responseTime || 0,
          log.sentTime ? new Date(log.sentTime).toLocaleString() : '',
          log.receiveTime ? new Date(log.receiveTime).toLocaleString() : '',
          log.response,
          log.failureReason || '',
          log.deviceOnline ? 'Yes' : 'No',
          log.signalStrength || ''
        ]);

        // Create CSV content
        const csvContent = [
          csvHeaders.join(','),
          ...csvData.map(row => row.map(field => `"${field}"`).join(','))
        ].join('\n');

        // Create and save file
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const fileName = `command-statistics-${filters.startDate.toISOString().split('T')[0]}-to-${filters.endDate.toISOString().split('T')[0]}.csv`;
        saveAs(blob, fileName);
      }
    } catch (error) {
      console.error('Error exporting to CSV:', error);
      alert('Failed to export data to CSV');
    } finally {
      setLoading(false);
      handleClose();
    }
  };

  const exportToPDF = () => {
    // PDF export functionality would be implemented here
    // For now, show a placeholder
    alert('PDF export functionality will be implemented in a future update');
    handleClose();
  };

  const exportChartImage = () => {
    // Chart image export functionality would be implemented here
    // For now, show a placeholder
    alert('Chart image export functionality will be implemented in a future update');
    handleClose();
  };

  return (
    <>
      <Button
        variant="outlined"
        startIcon={loading ? <CircularProgress size={20} /> : <GetAppIcon />}
        onClick={handleClick}
        disabled={loading}
      >
        Export
      </Button>
      
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <MenuItem onClick={exportToExcel}>
          <ListItemIcon>
            <TableChartIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Export to Excel</ListItemText>
        </MenuItem>
        
        <MenuItem onClick={exportToCSV}>
          <ListItemIcon>
            <TableChartIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Export to CSV</ListItemText>
        </MenuItem>
        
        <MenuItem onClick={exportToPDF}>
          <ListItemIcon>
            <PictureAsPdfIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Export to PDF</ListItemText>
        </MenuItem>
        
        <MenuItem onClick={exportChartImage}>
          <ListItemIcon>
            <ImageIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Export Charts as Images</ListItemText>
        </MenuItem>
      </Menu>
    </>
  );
}
