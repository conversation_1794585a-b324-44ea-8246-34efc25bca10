import React from 'react';
import PropTypes from 'prop-types';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  LinearProgress
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import TrendingFlatIcon from '@mui/icons-material/TrendingFlat';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import ReceiptIcon from '@mui/icons-material/Receipt';
import SubscriptionsIcon from '@mui/icons-material/Subscriptions';
import BuildIcon from '@mui/icons-material/Build';

IncomeMetricsCards.propTypes = {
  data: PropTypes.object,
  growth: PropTypes.object,
  loading: PropTypes.bool
};

export default function IncomeMetricsCards({ data, growth, loading }) {
  const theme = useTheme();
  const { t } = useTranslation();

  if (!data) {
    return null;
  }

  const formatCurrency = (amount) => {
    return `₮${amount.toLocaleString()}`;
  };

  const formatNumber = (number) => {
    return number.toLocaleString();
  };

  const getTrendIcon = (value) => {
    if (value > 0) return <TrendingUpIcon sx={{ color: theme.palette.success.main }} />;
    if (value < 0) return <TrendingDownIcon sx={{ color: theme.palette.error.main }} />;
    return <TrendingFlatIcon sx={{ color: theme.palette.grey[500] }} />;
  };

  const getTrendColor = (value) => {
    if (value > 0) return 'success';
    if (value < 0) return 'error';
    return 'default';
  };

  const metrics = [
    {
      title: t('income.metrics.total_income', 'Total Income'),
      value: formatCurrency(data.totalIncome),
      icon: <MonetizationOnIcon sx={{ fontSize: 40, color: theme.palette.primary.main }} />,
      growth: growth?.growth?.income,
      description: t('income.metrics.total_income_desc', 'Combined revenue from all sources')
    },
    {
      title: t('income.metrics.total_transactions', 'Total Transactions'),
      value: formatNumber(data.totalTransactions),
      icon: <ReceiptIcon sx={{ fontSize: 40, color: theme.palette.info.main }} />,
      growth: growth?.growth?.transactions,
      description: t('income.metrics.total_transactions_desc', 'Orders and subscriptions combined')
    },
    {
      title: t('income.metrics.order_income', 'Installation Orders'),
      value: formatCurrency(data.orderIncome.totalOrderIncome),
      icon: <BuildIcon sx={{ fontSize: 40, color: theme.palette.warning.main }} />,
      subtitle: `${formatNumber(data.orderIncome.totalOrders)} orders`,
      description: t('income.metrics.order_income_desc', 'Revenue from device installations')
    },
    {
      title: t('income.metrics.license_income', 'License Subscriptions'),
      value: formatCurrency(data.licenseIncome.totalLicenseIncome),
      icon: <SubscriptionsIcon sx={{ fontSize: 40, color: theme.palette.secondary.main }} />,
      subtitle: `${formatNumber(data.licenseIncome.totalLicenses)} licenses`,
      description: t('income.metrics.license_income_desc', 'Revenue from monthly subscriptions')
    }
  ];

  return (
    <Grid container spacing={3} sx={{ mb: 3 }}>
      {metrics.map((metric, index) => (
        <Grid item xs={12} sm={6} md={3} key={index}>
          <Card 
            sx={{ 
              height: '100%',
              transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: theme.shadows[8]
              }
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 2 }}>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    {metric.title}
                  </Typography>
                  <Typography variant="h4" component="div" sx={{ fontWeight: 'bold', mb: 1 }}>
                    {metric.value}
                  </Typography>
                  {metric.subtitle && (
                    <Typography variant="body2" color="text.secondary">
                      {metric.subtitle}
                    </Typography>
                  )}
                </Box>
                <Box sx={{ ml: 2 }}>
                  {metric.icon}
                </Box>
              </Box>

              {/* Growth indicator */}
              {metric.growth !== undefined && (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  {getTrendIcon(metric.growth)}
                  <Chip
                    label={`${metric.growth > 0 ? '+' : ''}${metric.growth}%`}
                    size="small"
                    color={getTrendColor(metric.growth)}
                    sx={{ ml: 1 }}
                  />
                </Box>
              )}

              {/* Description */}
              <Typography variant="caption" color="text.secondary">
                {metric.description}
              </Typography>

              {/* Loading indicator */}
              {loading && (
                <Box sx={{ mt: 2 }}>
                  <LinearProgress />
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      ))}

      {/* Average Daily Income Card */}
      <Grid item xs={12} sm={6} md={3}>
        <Card 
          sx={{ 
            height: '100%',
            background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
            color: 'white'
          }}
        >
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 2 }}>
              <Box sx={{ flex: 1 }}>
                <Typography variant="subtitle2" sx={{ color: 'rgba(255,255,255,0.8)' }} gutterBottom>
                  {t('income.metrics.avg_daily', 'Average Daily Income')}
                </Typography>
                <Typography variant="h4" component="div" sx={{ fontWeight: 'bold', mb: 1 }}>
                  {formatCurrency(Math.round(data.averageDailyIncome))}
                </Typography>
                <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                  {t('income.metrics.over_period', 'Over selected period')}
                </Typography>
              </Box>
              <MonetizationOnIcon sx={{ fontSize: 40, color: 'rgba(255,255,255,0.8)' }} />
            </Box>

            <Typography variant="caption" sx={{ color: 'rgba(255,255,255,0.7)' }}>
              {t('income.metrics.avg_daily_desc', 'Daily average based on total income')}
            </Typography>

            {loading && (
              <Box sx={{ mt: 2 }}>
                <LinearProgress sx={{ backgroundColor: 'rgba(255,255,255,0.2)' }} />
              </Box>
            )}
          </CardContent>
        </Card>
      </Grid>

      {/* Income Distribution Progress */}
      <Grid item xs={12} sm={6} md={4}>
        <Card sx={{ height: '100%' }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              {t('income.metrics.income_distribution', 'Income Distribution')}
            </Typography>
            
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2">
                  {t('income.metrics.orders', 'Installation Orders')}
                </Typography>
                <Typography variant="body2" fontWeight="bold">
                  {data.totalIncome > 0 ? Math.round((data.orderIncome.totalOrderIncome / data.totalIncome) * 100) : 0}%
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={data.totalIncome > 0 ? (data.orderIncome.totalOrderIncome / data.totalIncome) * 100 : 0}
                sx={{ height: 8, borderRadius: 4 }}
                color="warning"
              />
            </Box>

            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2">
                  {t('income.metrics.licenses', 'License Subscriptions')}
                </Typography>
                <Typography variant="body2" fontWeight="bold">
                  {data.totalIncome > 0 ? Math.round((data.licenseIncome.totalLicenseIncome / data.totalIncome) * 100) : 0}%
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={data.totalIncome > 0 ? (data.licenseIncome.totalLicenseIncome / data.totalIncome) * 100 : 0}
                sx={{ height: 8, borderRadius: 4 }}
                color="secondary"
              />
            </Box>

            <Typography variant="caption" color="text.secondary" sx={{ mt: 2, display: 'block' }}>
              {t('income.metrics.distribution_desc', 'Percentage breakdown of revenue sources')}
            </Typography>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
}
