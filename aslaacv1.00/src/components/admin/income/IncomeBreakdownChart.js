import React from 'react';
import PropTypes from 'prop-types';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Grid,
  LinearProgress,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend
} from 'chart.js';
import { Pie } from 'react-chartjs-2';
import BuildIcon from '@mui/icons-material/Build';
import SubscriptionsIcon from '@mui/icons-material/Subscriptions';

// Register ChartJS components
ChartJS.register(ArcElement, Tooltip, Legend);

IncomeBreakdownChart.propTypes = {
  data: PropTypes.object,
  detailed: PropTypes.bool
};

export default function IncomeBreakdownChart({ data, detailed = false }) {
  const theme = useTheme();
  const { t } = useTranslation();

  if (!data || !data.breakdown) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {t('income.charts.breakdown', 'Income Breakdown')}
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
            <Typography variant="body2" color="text.secondary">
              {t('income.no_data', 'No data available')}
            </Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  const formatCurrency = (amount) => {
    return `₮${amount.toLocaleString()}`;
  };

  // Prepare data for pie chart
  const pieData = {
    labels: data.breakdown.map(item => item.label),
    datasets: [
      {
        data: data.breakdown.map(item => item.income),
        backgroundColor: [
          theme.palette.warning.main,
          theme.palette.secondary.main,
          theme.palette.primary.main,
          theme.palette.info.main
        ],
        borderColor: [
          theme.palette.warning.dark,
          theme.palette.secondary.dark,
          theme.palette.primary.dark,
          theme.palette.info.dark
        ],
        borderWidth: 2,
        hoverBackgroundColor: [
          theme.palette.warning.light,
          theme.palette.secondary.light,
          theme.palette.primary.light,
          theme.palette.info.light
        ]
      }
    ]
  };

  const pieOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: detailed ? 'right' : 'bottom',
        labels: {
          padding: 20,
          usePointStyle: true,
          font: {
            size: 12
          }
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.label || '';
            const value = formatCurrency(context.parsed);
            const percentage = data.breakdown[context.dataIndex].percentage;
            return `${label}: ${value} (${percentage}%)`;
          }
        }
      }
    }
  };

  const getIcon = (source) => {
    switch (source) {
      case 'orders':
        return <BuildIcon sx={{ color: theme.palette.warning.main }} />;
      case 'licenses':
        return <SubscriptionsIcon sx={{ color: theme.palette.secondary.main }} />;
      default:
        return null;
    }
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {t('income.charts.breakdown', 'Income Breakdown')}
        </Typography>
        
        {detailed ? (
          <Grid container spacing={3}>
            {/* Detailed Chart */}
            <Grid item xs={12} md={8}>
              <Box sx={{ height: 400 }}>
                <Pie data={pieData} options={pieOptions} />
              </Box>
            </Grid>

            {/* Detailed Table */}
            <Grid item xs={12} md={4}>
              <TableContainer component={Paper} variant="outlined">
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>{t('income.breakdown.source', 'Source')}</TableCell>
                      <TableCell align="right">{t('income.breakdown.amount', 'Amount')}</TableCell>
                      <TableCell align="right">{t('income.breakdown.percentage', '%')}</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {data.breakdown.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {getIcon(item.source)}
                            <Box>
                              <Typography variant="body2" fontWeight="medium">
                                {item.label}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {item.count} × {formatCurrency(item.unitPrice)}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="body2" fontWeight="bold">
                            {formatCurrency(item.income)}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Chip
                            label={`${item.percentage}%`}
                            size="small"
                            color={index === 0 ? 'warning' : 'secondary'}
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Grid>
          </Grid>
        ) : (
          <Grid container spacing={3}>
            {/* Simple Chart */}
            <Grid item xs={12} md={6}>
              <Box sx={{ height: 250 }}>
                <Pie data={pieData} options={pieOptions} />
              </Box>
            </Grid>

            {/* Breakdown List */}
            <Grid item xs={12} md={6}>
              <Box sx={{ mt: 2 }}>
                {data.breakdown.map((item, index) => (
                  <Box key={index} sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {getIcon(item.source)}
                        <Typography variant="body1" fontWeight="medium">
                          {item.label}
                        </Typography>
                      </Box>
                      <Chip
                        label={`${item.percentage}%`}
                        size="small"
                        color={index === 0 ? 'warning' : 'secondary'}
                      />
                    </Box>
                    
                    <LinearProgress
                      variant="determinate"
                      value={parseFloat(item.percentage)}
                      sx={{ 
                        height: 8, 
                        borderRadius: 4,
                        backgroundColor: theme.palette.grey[200]
                      }}
                      color={index === 0 ? 'warning' : 'secondary'}
                    />
                    
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                      <Typography variant="h6" color="primary">
                        {formatCurrency(item.income)}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {item.count} transactions × {formatCurrency(item.unitPrice)}
                      </Typography>
                    </Box>
                  </Box>
                ))}
              </Box>
            </Grid>
          </Grid>
        )}

        {/* Summary */}
        <Box sx={{ mt: 3, pt: 3, borderTop: 1, borderColor: 'divider' }}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h5" color="primary" fontWeight="bold">
                  {formatCurrency(data.totalIncome)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {t('income.breakdown.total_income', 'Total Income')}
                </Typography>
              </Box>
            </Grid>
            
            <Grid item xs={12} sm={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h5" color="info.main" fontWeight="bold">
                  {data.totalTransactions.toLocaleString()}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {t('income.breakdown.total_transactions', 'Total Transactions')}
                </Typography>
              </Box>
            </Grid>
            
            <Grid item xs={12} sm={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h5" color="success.main" fontWeight="bold">
                  {formatCurrency(Math.round(data.totalIncome / Math.max(data.totalTransactions, 1)))}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {t('income.breakdown.avg_transaction', 'Avg Transaction')}
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </CardContent>
    </Card>
  );
}
