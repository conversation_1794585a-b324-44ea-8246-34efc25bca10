import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  Button,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  CircularProgress,
  Alert,
  Snackbar
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import GetAppIcon from '@mui/icons-material/GetApp';
import TableChartIcon from '@mui/icons-material/TableChart';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import DataObjectIcon from '@mui/icons-material/DataObject';
import axios from '../../../utils/axios';

IncomeExportButton.propTypes = {
  filters: PropTypes.object.isRequired
};

export default function IncomeExportButton({ filters }) {
  const { t } = useTranslation();
  const [anchorEl, setAnchorEl] = useState(null);
  const [loading, setLoading] = useState(false);
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });
  const open = Boolean(anchorEl);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const showNotification = (message, severity = 'success') => {
    setNotification({ open: true, message, severity });
  };

  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };

  const exportToJSON = async () => {
    setLoading(true);
    try {
      const params = {
        startDate: filters.startDate.toISOString(),
        endDate: filters.endDate.toISOString(),
        format: 'json'
      };

      const response = await axios.get('/api/admin/income/export', { params });

      if (response.data.success) {
        // Create and download JSON file
        const jsonContent = JSON.stringify(response.data.data, null, 2);
        const blob = new Blob([jsonContent], { type: 'application/json' });
        const fileName = `income-report-${filters.startDate.toISOString().split('T')[0]}-to-${filters.endDate.toISOString().split('T')[0]}.json`;
        
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        showNotification(t('income.export.json_success', 'JSON export completed successfully'));
      }
    } catch (error) {
      console.error('Error exporting to JSON:', error);
      showNotification(t('income.export.json_error', 'Failed to export JSON data'), 'error');
    } finally {
      setLoading(false);
      handleClose();
    }
  };

  const exportToCSV = async () => {
    setLoading(true);
    try {
      const params = {
        startDate: filters.startDate.toISOString(),
        endDate: filters.endDate.toISOString(),
        format: 'csv'
      };

      const response = await axios.get('/api/admin/income/export', { 
        params,
        responseType: 'blob'
      });

      // Create and download CSV file
      const blob = new Blob([response.data], { type: 'text/csv' });
      const fileName = `income-report-${filters.startDate.toISOString().split('T')[0]}-to-${filters.endDate.toISOString().split('T')[0]}.csv`;
      
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      showNotification(t('income.export.csv_success', 'CSV export completed successfully'));
    } catch (error) {
      console.error('Error exporting to CSV:', error);
      showNotification(t('income.export.csv_error', 'Failed to export CSV data'), 'error');
    } finally {
      setLoading(false);
      handleClose();
    }
  };

  const exportToExcel = async () => {
    setLoading(true);
    try {
      const params = {
        startDate: filters.startDate.toISOString(),
        endDate: filters.endDate.toISOString(),
        format: 'json'
      };

      const response = await axios.get('/api/admin/income/export', { params });

      if (response.data.success) {
        // Import XLSX library dynamically
        const XLSX = await import('xlsx');
        
        const data = response.data.data;
        
        // Create workbook with multiple sheets
        const workbook = XLSX.utils.book_new();
        
        // Summary sheet
        const summaryData = [
          ['Income Report Summary'],
          [''],
          ['Date Range', `${filters.startDate.toLocaleDateString()} - ${filters.endDate.toLocaleDateString()}`],
          ['Total Income', `₮${data.summary.totalIncome.toLocaleString()}`],
          ['Total Transactions', data.summary.totalTransactions.toLocaleString()],
          ['Order Income', `₮${data.summary.orderIncome.toLocaleString()}`],
          ['License Income', `₮${data.summary.licenseIncome.toLocaleString()}`],
          ['Order Count', data.summary.orderCount.toLocaleString()],
          ['License Count', data.summary.licenseCount.toLocaleString()],
          [''],
          ['Generated on', new Date().toLocaleString()]
        ];
        
        const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
        XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');
        
        // All transactions sheet
        if (data.allTransactions && data.allTransactions.length > 0) {
          const transactionHeaders = ['Date', 'Type', 'Phone Number', 'Description', 'Amount', 'Invoice ID'];
          const transactionData = data.allTransactions.map(transaction => [
            new Date(transaction.date).toLocaleDateString(),
            transaction.type,
            transaction.phoneNumber,
            transaction.type === 'order' 
              ? `Installation Order - ${transaction.carModel}` 
              : `License Subscription - ${transaction.licenseType}`,
            transaction.amount,
            transaction.invoiceId || transaction.realInvoiceId || 'N/A'
          ]);
          
          const transactionSheet = XLSX.utils.aoa_to_sheet([transactionHeaders, ...transactionData]);
          XLSX.utils.book_append_sheet(workbook, transactionSheet, 'Transactions');
        }
        
        // Orders sheet
        if (data.orders && data.orders.length > 0) {
          const orderHeaders = ['Date', 'Phone Number', 'Car Model', 'Amount', 'Invoice ID', 'Real Invoice ID'];
          const orderData = data.orders.map(order => [
            new Date(order.date).toLocaleDateString(),
            order.phoneNumber,
            order.carModel,
            order.amount,
            order.invoiceId || 'N/A',
            order.realInvoiceId || 'N/A'
          ]);
          
          const orderSheet = XLSX.utils.aoa_to_sheet([orderHeaders, ...orderData]);
          XLSX.utils.book_append_sheet(workbook, orderSheet, 'Orders');
        }
        
        // Licenses sheet
        if (data.licenses && data.licenses.length > 0) {
          const licenseHeaders = ['Date', 'Phone Number', 'Username', 'Amount', 'License Type'];
          const licenseData = data.licenses.map(license => [
            new Date(license.date).toLocaleDateString(),
            license.phoneNumber,
            license.username,
            license.amount,
            license.licenseType
          ]);
          
          const licenseSheet = XLSX.utils.aoa_to_sheet([licenseHeaders, ...licenseData]);
          XLSX.utils.book_append_sheet(workbook, licenseSheet, 'Licenses');
        }
        
        // Save file
        const fileName = `income-report-${filters.startDate.toISOString().split('T')[0]}-to-${filters.endDate.toISOString().split('T')[0]}.xlsx`;
        XLSX.writeFile(workbook, fileName);

        showNotification(t('income.export.excel_success', 'Excel export completed successfully'));
      }
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      showNotification(t('income.export.excel_error', 'Failed to export Excel data'), 'error');
    } finally {
      setLoading(false);
      handleClose();
    }
  };

  const exportToPDF = () => {
    // PDF export functionality placeholder
    showNotification(t('income.export.pdf_placeholder', 'PDF export functionality will be implemented in a future update'), 'info');
    handleClose();
  };

  return (
    <>
      <Button
        variant="outlined"
        startIcon={loading ? <CircularProgress size={20} /> : <GetAppIcon />}
        onClick={handleClick}
        disabled={loading}
      >
        {t('income.export.button', 'Export Data')}
      </Button>
      
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <MenuItem onClick={exportToExcel}>
          <ListItemIcon>
            <TableChartIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('income.export.excel', 'Export to Excel')}</ListItemText>
        </MenuItem>
        
        <MenuItem onClick={exportToCSV}>
          <ListItemIcon>
            <TableChartIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('income.export.csv', 'Export to CSV')}</ListItemText>
        </MenuItem>
        
        <MenuItem onClick={exportToJSON}>
          <ListItemIcon>
            <DataObjectIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('income.export.json', 'Export to JSON')}</ListItemText>
        </MenuItem>
        
        <MenuItem onClick={exportToPDF}>
          <ListItemIcon>
            <PictureAsPdfIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('income.export.pdf', 'Export to PDF')}</ListItemText>
        </MenuItem>
      </Menu>

      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
      >
        <Alert onClose={handleCloseNotification} severity={notification.severity}>
          {notification.message}
        </Alert>
      </Snackbar>
    </>
  );
}
