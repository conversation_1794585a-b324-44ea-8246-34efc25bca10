import React from 'react';
import PropTypes from 'prop-types';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Grid,
  Chip
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title
} from 'chart.js';
import { Doughnut, Bar } from 'react-chartjs-2';

// Register ChartJS components
ChartJS.register(
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title
);

IncomeOverviewChart.propTypes = {
  data: PropTypes.object
};

export default function IncomeOverviewChart({ data }) {
  const theme = useTheme();
  const { t } = useTranslation();

  if (!data) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {t('income.charts.overview', 'Income Overview')}
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
            <Typography variant="body2" color="text.secondary">
              {t('income.no_data', 'No data available')}
            </Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  const formatCurrency = (amount) => {
    return `₮${amount.toLocaleString()}`;
  };

  // Prepare data for doughnut chart
  const doughnutData = {
    labels: [
      t('income.sources.orders', 'Installation Orders'),
      t('income.sources.licenses', 'License Subscriptions')
    ],
    datasets: [
      {
        data: [
          data.orderIncome.totalOrderIncome,
          data.licenseIncome.totalLicenseIncome
        ],
        backgroundColor: [
          theme.palette.warning.main,
          theme.palette.secondary.main
        ],
        borderColor: [
          theme.palette.warning.dark,
          theme.palette.secondary.dark
        ],
        borderWidth: 2,
        hoverBackgroundColor: [
          theme.palette.warning.light,
          theme.palette.secondary.light
        ]
      }
    ]
  };

  // Prepare data for bar chart (transaction counts)
  const barData = {
    labels: [
      t('income.sources.orders_short', 'Orders'),
      t('income.sources.licenses_short', 'Licenses')
    ],
    datasets: [
      {
        label: t('income.charts.transaction_count', 'Transaction Count'),
        data: [
          data.orderIncome.totalOrders,
          data.licenseIncome.totalLicenses
        ],
        backgroundColor: [
          theme.palette.warning.main,
          theme.palette.secondary.main
        ],
        borderColor: [
          theme.palette.warning.dark,
          theme.palette.secondary.dark
        ],
        borderWidth: 1,
        borderRadius: 4
      }
    ]
  };

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          padding: 20,
          usePointStyle: true,
          font: {
            size: 12
          }
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.label || '';
            const value = formatCurrency(context.parsed);
            const percentage = ((context.parsed / data.totalIncome) * 100).toFixed(1);
            return `${label}: ${value} (${percentage}%)`;
          }
        }
      }
    },
    cutout: '60%'
  };

  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return `${context.dataset.label}: ${context.parsed.y.toLocaleString()}`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value) {
            return value.toLocaleString();
          }
        }
      }
    }
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {t('income.charts.overview', 'Income Overview')}
        </Typography>
        
        <Grid container spacing={3}>
          {/* Revenue Distribution */}
          <Grid item xs={12} md={6}>
            <Box sx={{ position: 'relative', height: 250 }}>
              <Typography variant="subtitle2" gutterBottom align="center">
                {t('income.charts.revenue_distribution', 'Revenue Distribution')}
              </Typography>
              <Doughnut data={doughnutData} options={doughnutOptions} />
              
              {/* Center text showing total */}
              <Box
                sx={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  textAlign: 'center',
                  pointerEvents: 'none'
                }}
              >
                <Typography variant="h6" fontWeight="bold">
                  {formatCurrency(data.totalIncome)}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {t('income.charts.total_revenue', 'Total Revenue')}
                </Typography>
              </Box>
            </Box>
          </Grid>

          {/* Transaction Counts */}
          <Grid item xs={12} md={6}>
            <Box sx={{ height: 250 }}>
              <Typography variant="subtitle2" gutterBottom align="center">
                {t('income.charts.transaction_counts', 'Transaction Counts')}
              </Typography>
              <Bar data={barData} options={barOptions} />
            </Box>
          </Grid>
        </Grid>

        {/* Summary Statistics */}
        <Box sx={{ mt: 3, pt: 3, borderTop: 1, borderColor: 'divider' }}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="warning.main" fontWeight="bold">
                  {formatCurrency(data.orderIncome.totalOrderIncome)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {t('income.sources.orders', 'Installation Orders')}
                </Typography>
                <Chip 
                  label={`${data.orderIncome.totalOrders} ${t('income.transactions', 'transactions')}`}
                  size="small"
                  color="warning"
                  sx={{ mt: 1 }}
                />
              </Box>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="secondary.main" fontWeight="bold">
                  {formatCurrency(data.licenseIncome.totalLicenseIncome)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {t('income.sources.licenses', 'License Subscriptions')}
                </Typography>
                <Chip 
                  label={`${data.licenseIncome.totalLicenses} ${t('income.transactions', 'transactions')}`}
                  size="small"
                  color="secondary"
                  sx={{ mt: 1 }}
                />
              </Box>
            </Grid>
          </Grid>

          {/* Key Insights */}
          <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
            <Typography variant="subtitle2" gutterBottom>
              {t('income.charts.key_insights', 'Key Insights')}
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  <strong>{t('income.charts.avg_order_value', 'Average Order Value')}:</strong> ₮20,000
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  <strong>{t('income.charts.avg_license_value', 'Average License Value')}:</strong> ₮5,000
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  <strong>{t('income.charts.total_transactions', 'Total Transactions')}:</strong> {data.totalTransactions.toLocaleString()}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  <strong>{t('income.charts.avg_daily_income', 'Avg Daily Income')}:</strong> {formatCurrency(Math.round(data.averageDailyIncome))}
                </Typography>
              </Grid>
            </Grid>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
}
