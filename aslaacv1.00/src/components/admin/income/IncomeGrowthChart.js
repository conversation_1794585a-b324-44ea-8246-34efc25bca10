import React from 'react';
import PropTypes from 'prop-types';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Grid,
  Chip,
  Paper
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';
import { Bar } from 'react-chartjs-2';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import TrendingFlatIcon from '@mui/icons-material/TrendingFlat';
import CompareArrowsIcon from '@mui/icons-material/CompareArrows';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

IncomeGrowthChart.propTypes = {
  data: PropTypes.object,
  period: PropTypes.string
};

export default function IncomeGrowthChart({ data, period = 'monthly' }) {
  const theme = useTheme();
  const { t } = useTranslation();

  if (!data) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {t('income.charts.growth_analysis', 'Growth Analysis')}
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
            <Typography variant="body2" color="text.secondary">
              {t('income.no_data', 'No data available')}
            </Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  const formatCurrency = (amount) => {
    return `₮${amount.toLocaleString()}`;
  };

  const formatPeriod = (periodData) => {
    const start = new Date(periodData.start);
    const end = new Date(periodData.end);
    
    switch (period) {
      case 'daily':
        return start.toLocaleDateString();
      case 'yearly':
        return start.getFullYear().toString();
      default: // monthly
        return `${start.getFullYear()}-${String(start.getMonth() + 1).padStart(2, '0')}`;
    }
  };

  const getTrendIcon = (value) => {
    if (value > 0) return <TrendingUpIcon sx={{ color: theme.palette.success.main }} />;
    if (value < 0) return <TrendingDownIcon sx={{ color: theme.palette.error.main }} />;
    return <TrendingFlatIcon sx={{ color: theme.palette.grey[500] }} />;
  };

  const getTrendColor = (value) => {
    if (value > 0) return 'success';
    if (value < 0) return 'error';
    return 'default';
  };

  // Prepare data for comparison chart
  const chartData = {
    labels: [
      t('income.growth.current_period', 'Current Period'),
      t('income.growth.previous_period', 'Previous Period')
    ],
    datasets: [
      {
        label: t('income.growth.total_income', 'Total Income'),
        data: [data.current.income, data.previous.income],
        backgroundColor: [
          theme.palette.primary.main,
          theme.palette.primary.light
        ],
        borderColor: [
          theme.palette.primary.dark,
          theme.palette.primary.main
        ],
        borderWidth: 1
      }
    ]
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return `${context.dataset.label}: ${formatCurrency(context.parsed.y)}`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value) {
            return formatCurrency(value);
          }
        }
      }
    }
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {t('income.charts.growth_analysis', 'Growth Analysis')} - {t(`income.filters.${period}`, period)}
        </Typography>
        
        <Grid container spacing={3}>
          {/* Growth Comparison Chart */}
          <Grid item xs={12} md={6}>
            <Box sx={{ height: 300 }}>
              <Typography variant="subtitle2" gutterBottom>
                {t('income.growth.period_comparison', 'Period Comparison')}
              </Typography>
              <Bar data={chartData} options={chartOptions} />
            </Box>
          </Grid>

          {/* Growth Metrics */}
          <Grid item xs={12} md={6}>
            <Grid container spacing={2}>
              {/* Income Growth */}
              <Grid item xs={12}>
                <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="subtitle2">
                      {t('income.growth.income_growth', 'Income Growth')}
                    </Typography>
                    {getTrendIcon(data.growth.income)}
                  </Box>
                  
                  <Typography variant="h4" color={getTrendColor(data.growth.income) + '.main'} fontWeight="bold">
                    {data.growth.income > 0 ? '+' : ''}{data.growth.income}%
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary">
                    {formatCurrency(data.growth.incomeChange)} change
                  </Typography>
                  
                  <Chip
                    label={data.growth.income > 0 ? t('income.growth.increase', 'Increase') : data.growth.income < 0 ? t('income.growth.decrease', 'Decrease') : t('income.growth.no_change', 'No Change')}
                    size="small"
                    color={getTrendColor(data.growth.income)}
                    sx={{ mt: 1 }}
                  />
                </Paper>
              </Grid>

              {/* Transaction Growth */}
              <Grid item xs={12}>
                <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="subtitle2">
                      {t('income.growth.transaction_growth', 'Transaction Growth')}
                    </Typography>
                    {getTrendIcon(data.growth.transactions)}
                  </Box>
                  
                  <Typography variant="h4" color={getTrendColor(data.growth.transactions) + '.main'} fontWeight="bold">
                    {data.growth.transactions > 0 ? '+' : ''}{data.growth.transactions}%
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary">
                    {data.growth.transactionChange > 0 ? '+' : ''}{data.growth.transactionChange} transactions
                  </Typography>
                  
                  <Chip
                    label={data.growth.transactions > 0 ? t('income.growth.increase', 'Increase') : data.growth.transactions < 0 ? t('income.growth.decrease', 'Decrease') : t('income.growth.no_change', 'No Change')}
                    size="small"
                    color={getTrendColor(data.growth.transactions)}
                    sx={{ mt: 1 }}
                  />
                </Paper>
              </Grid>
            </Grid>
          </Grid>

          {/* Period Details */}
          <Grid item xs={12}>
            <Grid container spacing={2}>
              {/* Current Period */}
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 2, border: 2, borderColor: 'primary.main' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <CompareArrowsIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h6" color="primary">
                      {t('income.growth.current_period', 'Current Period')}
                    </Typography>
                  </Box>
                  
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {formatPeriod(data.current.period)}
                  </Typography>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">
                        {t('income.growth.income', 'Income')}
                      </Typography>
                      <Typography variant="h6" fontWeight="bold">
                        {formatCurrency(data.current.income)}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">
                        {t('income.growth.transactions', 'Transactions')}
                      </Typography>
                      <Typography variant="h6" fontWeight="bold">
                        {data.current.transactions.toLocaleString()}
                      </Typography>
                    </Grid>
                  </Grid>
                </Paper>
              </Grid>

              {/* Previous Period */}
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 2, border: 1, borderColor: 'grey.300' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <CompareArrowsIcon sx={{ mr: 1, color: 'grey.500' }} />
                    <Typography variant="h6" color="text.secondary">
                      {t('income.growth.previous_period', 'Previous Period')}
                    </Typography>
                  </Box>
                  
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {formatPeriod(data.previous.period)}
                  </Typography>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">
                        {t('income.growth.income', 'Income')}
                      </Typography>
                      <Typography variant="h6" fontWeight="bold">
                        {formatCurrency(data.previous.income)}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">
                        {t('income.growth.transactions', 'Transactions')}
                      </Typography>
                      <Typography variant="h6" fontWeight="bold">
                        {data.previous.transactions.toLocaleString()}
                      </Typography>
                    </Grid>
                  </Grid>
                </Paper>
              </Grid>
            </Grid>
          </Grid>

          {/* Growth Insights */}
          <Grid item xs={12}>
            <Paper sx={{ p: 2, bgcolor: 'info.light', color: 'info.contrastText' }}>
              <Typography variant="subtitle2" gutterBottom>
                {t('income.growth.insights', 'Growth Insights')}
              </Typography>
              
              <Typography variant="body2">
                {data.growth.income > 0 
                  ? t('income.growth.positive_insight', `Income has grown by ${data.growth.income}% compared to the previous ${period}. This represents an increase of ${formatCurrency(data.growth.incomeChange)}.`)
                  : data.growth.income < 0
                  ? t('income.growth.negative_insight', `Income has decreased by ${Math.abs(data.growth.income)}% compared to the previous ${period}. This represents a decrease of ${formatCurrency(Math.abs(data.growth.incomeChange))}.`)
                  : t('income.growth.neutral_insight', `Income has remained stable compared to the previous ${period}.`)
                }
              </Typography>
            </Paper>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
}
