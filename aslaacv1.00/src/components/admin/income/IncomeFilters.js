import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  Paper,
  Box,
  Typography,
  Grid,
  TextField,
  MenuItem,
  Button,
  IconButton,
  Tooltip,
  Collapse
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { useTranslation } from 'react-i18next';
import FilterListIcon from '@mui/icons-material/FilterList';
import RefreshIcon from '@mui/icons-material/Refresh';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import TodayIcon from '@mui/icons-material/Today';
import DateRangeIcon from '@mui/icons-material/DateRange';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';

IncomeFilters.propTypes = {
  filters: PropTypes.object.isRequired,
  onFilterChange: PropTypes.func.isRequired,
  onRefresh: PropTypes.func.isRequired,
  loading: PropTypes.bool
};

export default function IncomeFilters({ filters, onFilterChange, onRefresh, loading }) {
  const { t } = useTranslation();
  const [expanded, setExpanded] = useState(false);
  const [localFilters, setLocalFilters] = useState(filters);

  const handleExpandClick = () => {
    setExpanded(!expanded);
  };

  const handleDateChange = (field, date) => {
    setLocalFilters({
      ...localFilters,
      [field]: date
    });
  };

  const handlePeriodChange = (e) => {
    setLocalFilters({
      ...localFilters,
      period: e.target.value
    });
  };

  const handleApplyFilters = () => {
    onFilterChange(localFilters);
  };

  const handleResetFilters = () => {
    const defaultFilters = {
      startDate: new Date(new Date().getFullYear(), new Date().getMonth() - 11, 1),
      endDate: new Date(),
      period: 'monthly'
    };
    setLocalFilters(defaultFilters);
    onFilterChange(defaultFilters);
  };

  const handleQuickDateRange = (range) => {
    const now = new Date();
    let startDate, endDate = new Date();

    switch (range) {
      case 'today':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'thisWeek':
        const startOfWeek = new Date(now);
        startOfWeek.setDate(now.getDate() - now.getDay());
        startDate = startOfWeek;
        break;
      case 'thisMonth':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'last3Months':
        startDate = new Date(now.getFullYear(), now.getMonth() - 2, 1);
        break;
      case 'last6Months':
        startDate = new Date(now.getFullYear(), now.getMonth() - 5, 1);
        break;
      case 'thisYear':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      case 'lastYear':
        startDate = new Date(now.getFullYear() - 1, 0, 1);
        endDate = new Date(now.getFullYear() - 1, 11, 31);
        break;
      default:
        return;
    }

    const newFilters = {
      ...localFilters,
      startDate,
      endDate
    };
    setLocalFilters(newFilters);
    onFilterChange(newFilters);
  };

  const periodOptions = [
    { value: 'daily', label: t('income.filters.daily', 'Daily') },
    { value: 'monthly', label: t('income.filters.monthly', 'Monthly') },
    { value: 'yearly', label: t('income.filters.yearly', 'Yearly') }
  ];

  const quickRanges = [
    { key: 'today', label: t('income.filters.today', 'Today'), icon: <TodayIcon /> },
    { key: 'thisWeek', label: t('income.filters.this_week', 'This Week'), icon: <DateRangeIcon /> },
    { key: 'thisMonth', label: t('income.filters.this_month', 'This Month'), icon: <CalendarMonthIcon /> },
    { key: 'last3Months', label: t('income.filters.last_3_months', 'Last 3 Months'), icon: <CalendarMonthIcon /> },
    { key: 'last6Months', label: t('income.filters.last_6_months', 'Last 6 Months'), icon: <CalendarMonthIcon /> },
    { key: 'thisYear', label: t('income.filters.this_year', 'This Year'), icon: <CalendarMonthIcon /> },
    { key: 'lastYear', label: t('income.filters.last_year', 'Last Year'), icon: <CalendarMonthIcon /> }
  ];

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Paper sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <FilterListIcon sx={{ mr: 1 }} />
            <Typography variant="subtitle1">
              {t('income.filters.title', 'Income Filters')}
            </Typography>
          </Box>
          
          <Box>
            <Tooltip title={t('income.filters.refresh', 'Refresh Data')}>
              <IconButton onClick={onRefresh} disabled={loading}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
            
            <Tooltip title={expanded ? t('income.filters.collapse', 'Collapse Filters') : t('income.filters.expand', 'Expand Filters')}>
              <IconButton onClick={handleExpandClick}>
                {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Basic Filters - Always Visible */}
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={3}>
            <DatePicker
              label={t('income.filters.start_date', 'Start Date')}
              value={localFilters.startDate}
              onChange={(date) => handleDateChange('startDate', date)}
              renderInput={(params) => <TextField {...params} fullWidth size="small" />}
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <DatePicker
              label={t('income.filters.end_date', 'End Date')}
              value={localFilters.endDate}
              onChange={(date) => handleDateChange('endDate', date)}
              renderInput={(params) => <TextField {...params} fullWidth size="small" />}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <TextField
              select
              label={t('income.filters.period', 'Period')}
              value={localFilters.period}
              onChange={handlePeriodChange}
              fullWidth
              size="small"
            >
              {periodOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button 
                variant="contained" 
                color="primary" 
                onClick={handleApplyFilters}
                disabled={loading}
                fullWidth
              >
                {t('income.filters.apply', 'Apply')}
              </Button>
              <Button
                variant="outlined"
                onClick={handleResetFilters}
                disabled={loading}
              >
                {t('income.filters.reset', 'Reset')}
              </Button>
            </Box>
          </Grid>
        </Grid>

        {/* Advanced Filters - Collapsible */}
        <Collapse in={expanded} timeout="auto" unmountOnExit>
          <Box sx={{ mt: 3, pt: 3, borderTop: 1, borderColor: 'divider' }}>
            <Typography variant="subtitle2" gutterBottom>
              {t('income.filters.quick_ranges', 'Quick Date Ranges')}
            </Typography>
            
            <Grid container spacing={1}>
              {quickRanges.map((range) => (
                <Grid item key={range.key}>
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={range.icon}
                    onClick={() => handleQuickDateRange(range.key)}
                    sx={{ 
                      textTransform: 'none',
                      borderRadius: 2
                    }}
                  >
                    {range.label}
                  </Button>
                </Grid>
              ))}
            </Grid>

            {/* Current Filter Summary */}
            <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                {t('income.filters.current_selection', 'Current Selection')}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                <strong>{t('income.filters.date_range', 'Date Range')}:</strong> {' '}
                {localFilters.startDate?.toLocaleDateString()} - {localFilters.endDate?.toLocaleDateString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                <strong>{t('income.filters.grouping', 'Grouping')}:</strong> {' '}
                {periodOptions.find(p => p.value === localFilters.period)?.label}
              </Typography>
            </Box>
          </Box>
        </Collapse>
      </Paper>
    </LocalizationProvider>
  );
}
