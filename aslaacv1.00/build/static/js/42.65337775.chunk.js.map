{"version": 3, "sources": ["../node_modules/framer-motion/dist/es/render/dom/features-animation.js", "../node_modules/framer-motion/dist/es/render/dom/features-max.js", "components/animate/features.js"], "names": ["domAnimation", "__assign", "renderer", "createDomVisualElement", "animations", "gestureAnimations", "domMax", "drag", "layoutAnimations"], "mappings": "mKAQIA,EAAeC,YAASA,YAAS,CAAEC,SAAUC,KAA0BC,KAAaC,KCApFC,EAASL,YAASA,YAASA,YAAS,CAAC,EAAGD,GAAeO,KAAOC,KCNnDF,W", "file": "static/js/42.65337775.chunk.js", "sourcesContent": ["import { __assign } from 'tslib';\nimport { animations } from '../../motion/features/animations.js';\nimport { gestureAnimations } from '../../motion/features/gestures.js';\nimport { createDomVisualElement } from './create-visual-element.js';\n\n/**\n * @public\n */\nvar domAnimation = __assign(__assign({ renderer: createDomVisualElement }, animations), gestureAnimations);\n\nexport { domAnimation };\n", "import { __assign } from 'tslib';\nimport { drag } from '../../motion/features/drag.js';\nimport { layoutAnimations } from '../../motion/features/layout/index.js';\nimport { domAnimation } from './features-animation.js';\n\n/**\n * @public\n */\nvar domMax = __assign(__assign(__assign({}, domAnimation), drag), layoutAnimations);\n\nexport { domMax };\n", "import { domMax } from 'framer-motion';\n\nexport default domMax;\n"], "sourceRoot": ""}