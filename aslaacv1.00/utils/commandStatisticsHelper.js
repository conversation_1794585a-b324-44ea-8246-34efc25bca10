const LogModel = require("../models/log");
const statisticsMqttPublisher = require("./statisticsMqtt");

class CommandStatisticsHelper {
  constructor() {
    this.pendingCommands = new Map();
  }

  // Track command sent to device
  async trackCommandSent(userId, deviceNumber, command, commandType = 'other', additionalData = {}) {
    try {
      const commandId = `${deviceNumber}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const sentTime = new Date();
      
      // Create log entry
      const log = new LogModel({
        user: userId,
        userId: userId,
        deviceNumber: deviceNumber,
        command: command,
        commandType: commandType,
        sent: "yes",
        success: null, // Will be updated when response is received
        sentTime: sentTime,
        responseType: "MQTT",
        deviceOnline: true,
        ...additionalData
      });
      
      await log.save();
      
      // Store pending command for response tracking
      this.pendingCommands.set(commandId, {
        logId: log._id,
        userId,
        deviceNumber,
        command,
        commandType,
        sentTime: sentTime.getTime(),
        log
      });
      
      // Publish to MQTT statistics
      statisticsMqttPublisher.publishCommandUpdate({
        userId,
        deviceNumber,
        command,
        commandType,
        sentTime: sentTime.getTime(),
        status: 'sent'
      });
      
      // Set timeout for command
      setTimeout(() => {
        this.handleCommandTimeout(commandId);
      }, 30000); // 30 second timeout
      
      console.log(`Command Statistics: Tracked command ${commandId} sent to device ${deviceNumber}`);
      return commandId;
      
    } catch (error) {
      console.error('Command Statistics: Error tracking command sent', error);
      return null;
    }
  }

  // Track command response received
  async trackCommandResponse(deviceNumber, response, success = null) {
    try {
      // Find pending command for this device
      const commandId = this.findPendingCommandForDevice(deviceNumber);
      
      if (!commandId) {
        console.log(`Command Statistics: No pending command found for device ${deviceNumber}`);
        return null;
      }
      
      const commandData = this.pendingCommands.get(commandId);
      const receiveTime = new Date();
      const responseTime = receiveTime.getTime() - commandData.sentTime;
      
      // Determine success if not provided
      if (success === null) {
        success = this.isSuccessResponse(response);
      }
      
      const responseStatus = this.getResponseStatus(response, success);
      
      // Update log entry
      await LogModel.findByIdAndUpdate(commandData.logId, {
        response: response,
        receiveTime: receiveTime,
        responseTime: responseTime,
        success: success,
        responseStatus: responseStatus,
        failureReason: success ? null : this.getFailureReason(response)
      });
      
      // Publish to MQTT statistics
      statisticsMqttPublisher.publishCommandUpdate({
        userId: commandData.userId,
        deviceNumber: commandData.deviceNumber,
        command: commandData.command,
        commandType: commandData.commandType,
        sentTime: commandData.sentTime,
        receiveTime: receiveTime.getTime(),
        responseTime: responseTime,
        response: response,
        success: success,
        responseStatus: responseStatus
      });
      
      // Remove from pending commands
      this.pendingCommands.delete(commandId);
      
      console.log(`Command Statistics: Tracked response for command ${commandId} from device ${deviceNumber}`);
      return commandId;
      
    } catch (error) {
      console.error('Command Statistics: Error tracking command response', error);
      return null;
    }
  }

  // Handle command timeout
  async handleCommandTimeout(commandId) {
    try {
      if (!this.pendingCommands.has(commandId)) {
        return; // Command already processed
      }
      
      const commandData = this.pendingCommands.get(commandId);
      const timeoutTime = new Date();
      const responseTime = timeoutTime.getTime() - commandData.sentTime;
      
      // Update log entry
      await LogModel.findByIdAndUpdate(commandData.logId, {
        response: "TIMEOUT - No response received",
        receiveTime: timeoutTime,
        responseTime: responseTime,
        success: false,
        responseStatus: 'timeout',
        failureReason: 'Device did not respond within timeout period',
        deviceOnline: false
      });
      
      // Publish to MQTT statistics
      statisticsMqttPublisher.publishCommandUpdate({
        userId: commandData.userId,
        deviceNumber: commandData.deviceNumber,
        command: commandData.command,
        commandType: commandData.commandType,
        sentTime: commandData.sentTime,
        receiveTime: timeoutTime.getTime(),
        responseTime: responseTime,
        response: "TIMEOUT",
        success: false,
        responseStatus: 'timeout',
        failureReason: 'Device did not respond within timeout period'
      });
      
      // Remove from pending commands
      this.pendingCommands.delete(commandId);
      
      console.log(`Command Statistics: Command ${commandId} timed out for device ${commandData.deviceNumber}`);
      
    } catch (error) {
      console.error('Command Statistics: Error handling command timeout', error);
    }
  }

  // Find pending command for device
  findPendingCommandForDevice(deviceNumber) {
    for (const [commandId, commandData] of this.pendingCommands.entries()) {
      if (commandData.deviceNumber === deviceNumber) {
        return commandId;
      }
    }
    return null;
  }

  // Determine if response indicates success
  isSuccessResponse(response) {
    const successIndicators = ['OK', 'SUCCESS', 'DONE', 'ACK', 'COMPLETED', 'READY'];
    const errorIndicators = ['ERROR', 'FAIL', 'TIMEOUT', 'INVALID', 'DENIED', 'OFFLINE'];
    
    const upperResponse = response.toUpperCase();
    
    if (successIndicators.some(indicator => upperResponse.includes(indicator))) {
      return true;
    }
    
    if (errorIndicators.some(indicator => upperResponse.includes(indicator))) {
      return false;
    }
    
    // Default to success if response is received
    return true;
  }

  // Get response status
  getResponseStatus(response, success) {
    if (success) {
      return 'success';
    }
    
    const upperResponse = response.toUpperCase();
    
    if (upperResponse.includes('TIMEOUT')) return 'timeout';
    if (upperResponse.includes('OFFLINE')) return 'device_offline';
    if (upperResponse.includes('INVALID')) return 'invalid_response';
    
    return 'failed';
  }

  // Get failure reason
  getFailureReason(response) {
    const upperResponse = response.toUpperCase();
    
    if (upperResponse.includes('TIMEOUT')) return 'Device response timeout';
    if (upperResponse.includes('OFFLINE')) return 'Device is offline';
    if (upperResponse.includes('INVALID')) return 'Invalid command or response';
    if (upperResponse.includes('DENIED')) return 'Command denied by device';
    if (upperResponse.includes('ERROR')) return 'Device reported error';
    
    return 'Unknown error';
  }

  // Get pending commands count
  getPendingCommandsCount() {
    return this.pendingCommands.size;
  }

  // Get pending commands for device
  getPendingCommandsForDevice(deviceNumber) {
    const commands = [];
    for (const [commandId, commandData] of this.pendingCommands.entries()) {
      if (commandData.deviceNumber === deviceNumber) {
        commands.push({
          commandId,
          ...commandData,
          waitTime: Date.now() - commandData.sentTime
        });
      }
    }
    return commands;
  }

  // Clear old pending commands (cleanup)
  clearOldPendingCommands(maxAge = 300000) { // 5 minutes default
    const now = Date.now();
    const toDelete = [];
    
    for (const [commandId, commandData] of this.pendingCommands.entries()) {
      if (now - commandData.sentTime > maxAge) {
        toDelete.push(commandId);
      }
    }
    
    toDelete.forEach(commandId => {
      this.handleCommandTimeout(commandId);
    });
    
    if (toDelete.length > 0) {
      console.log(`Command Statistics: Cleared ${toDelete.length} old pending commands`);
    }
  }

  // Start cleanup interval
  startCleanupInterval() {
    setInterval(() => {
      this.clearOldPendingCommands();
    }, 60000); // Run every minute
  }
}

// Create singleton instance
const commandStatisticsHelper = new CommandStatisticsHelper();

// Start cleanup interval
commandStatisticsHelper.startCleanupInterval();

module.exports = commandStatisticsHelper;
