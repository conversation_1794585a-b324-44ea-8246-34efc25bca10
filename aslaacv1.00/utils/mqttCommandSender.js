const { publish } = require("./mqtt");
const commandStatisticsHelper = require("./commandStatisticsHelper");

class MqttCommandSender {
  constructor() {
    this.responseHandlers = new Map();
    this.setupResponseListener();
  }

  // Setup MQTT response listener
  setupResponseListener() {
    // This would integrate with your existing MQTT setup
    // You'll need to modify your existing MQTT message handler to call this
    console.log('MQTT Command Sender: Response listener setup');
  }

  // Send command to device with statistics tracking
  async sendCommandToDevice(userId, deviceNumber, command, commandType = 'other', options = {}) {
    try {
      console.log(`MQTT Command Sender: Sending command to device ${deviceNumber}: ${command}`);
      
      // Track command sent
      const commandId = await commandStatisticsHelper.trackCommandSent(
        userId,
        deviceNumber,
        command,
        commandType,
        {
          message: options.message || '',
          responseType: 'MQTT'
        }
      );
      
      // Send command via MQTT to device topic
      const success = await publish(deviceNumber, command);
      
      if (!success) {
        // If MQTT publish failed, update the log
        await commandStatisticsHelper.trackCommandResponse(
          deviceNumber,
          'MQTT publish failed',
          false
        );
        
        return {
          success: false,
          error: 'Failed to send command via MQTT',
          commandId
        };
      }
      
      return {
        success: true,
        message: 'Command sent successfully',
        commandId,
        deviceNumber,
        command
      };
      
    } catch (error) {
      console.error('MQTT Command Sender: Error sending command', error);
      
      // Track failed command
      if (commandId) {
        await commandStatisticsHelper.trackCommandResponse(
          deviceNumber,
          `Error: ${error.message}`,
          false
        );
      }
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Handle device response (call this from your MQTT message handler)
  async handleDeviceResponse(deviceNumber, response) {
    try {
      console.log(`MQTT Command Sender: Received response from device ${deviceNumber}: ${response}`);
      
      // Track the response
      await commandStatisticsHelper.trackCommandResponse(deviceNumber, response);
      
      // Call any registered response handlers
      const handler = this.responseHandlers.get(deviceNumber);
      if (handler) {
        handler(response);
        this.responseHandlers.delete(deviceNumber);
      }
      
    } catch (error) {
      console.error('MQTT Command Sender: Error handling device response', error);
    }
  }

  // Register response handler for specific device
  onDeviceResponse(deviceNumber, handler, timeout = 30000) {
    this.responseHandlers.set(deviceNumber, handler);
    
    // Clear handler after timeout
    setTimeout(() => {
      if (this.responseHandlers.has(deviceNumber)) {
        this.responseHandlers.delete(deviceNumber);
        handler({ error: 'Response timeout' });
      }
    }, timeout);
  }

  // Send command and wait for response
  async sendCommandAndWaitForResponse(userId, deviceNumber, command, commandType = 'other', timeout = 30000) {
    return new Promise(async (resolve, reject) => {
      try {
        // Set up response handler
        this.onDeviceResponse(deviceNumber, (response) => {
          if (response.error) {
            reject(new Error(response.error));
          } else {
            resolve({
              success: true,
              command,
              response,
              deviceNumber
            });
          }
        }, timeout);
        
        // Send command
        const result = await this.sendCommandToDevice(userId, deviceNumber, command, commandType);
        
        if (!result.success) {
          this.responseHandlers.delete(deviceNumber);
          reject(new Error(result.error));
        }
        
      } catch (error) {
        this.responseHandlers.delete(deviceNumber);
        reject(error);
      }
    });
  }

  // Convenience methods for common commands
  async powerOn(userId, deviceNumber) {
    return this.sendCommandToDevice(userId, deviceNumber, 'POWER_ON', 'power_on');
  }

  async powerOff(userId, deviceNumber) {
    return this.sendCommandToDevice(userId, deviceNumber, 'POWER_OFF', 'power_off');
  }

  async lock(userId, deviceNumber) {
    return this.sendCommandToDevice(userId, deviceNumber, 'LOCK', 'lock');
  }

  async unlock(userId, deviceNumber) {
    return this.sendCommandToDevice(userId, deviceNumber, 'UNLOCK', 'unlock');
  }

  async getLocation(userId, deviceNumber) {
    return this.sendCommandToDevice(userId, deviceNumber, 'GET_LOCATION', 'location');
  }

  async getStatus(userId, deviceNumber) {
    return this.sendCommandToDevice(userId, deviceNumber, 'GET_STATUS', 'status');
  }

  async setConfig(userId, deviceNumber, configData) {
    const command = `SET_CONFIG:${JSON.stringify(configData)}`;
    return this.sendCommandToDevice(userId, deviceNumber, command, 'config');
  }

  // Get statistics for sent commands
  getPendingCommandsCount() {
    return commandStatisticsHelper.getPendingCommandsCount();
  }

  getPendingCommandsForDevice(deviceNumber) {
    return commandStatisticsHelper.getPendingCommandsForDevice(deviceNumber);
  }
}

// Create singleton instance
const mqttCommandSender = new MqttCommandSender();

module.exports = mqttCommandSender;
