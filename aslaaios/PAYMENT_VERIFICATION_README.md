# Automatic Payment Verification for iOS App

This document describes the automatic payment verification system implemented in the Flutter iOS app.

## Overview

The automatic payment verification system provides a seamless experience for license extensions by:
- Automatically checking payment status every 5 seconds
- Providing real-time progress updates to users
- Creating licenses automatically when payments are confirmed
- Showing visual feedback throughout the process

## Components

### 1. PaymentService (`lib/service/payment_service.dart`)
- Handles automatic payment verification
- Manages payment status streams
- Creates licenses manually as backup to webhooks
- Provides progress tracking and timeout handling

### 2. EnhancedPaymentDialog (`lib/widgets/enhanced_payment_dialog.dart`)
- Modern payment dialog with automatic verification
- Shows payment progress and status
- Includes manual check functionality
- Provides better user experience than the old dialog

### 3. Updated LicenseWidget (`lib/license/license_widget.dart`)
- Integrated with payment verification system
- Shows payment status indicators
- Disables button during payment processing
- Handles payment success automatically

## Features

### Automatic Payment Checking
- Starts immediately when payment dialog opens
- Checks every 5 seconds for up to 5 minutes
- Shows progress bar and percentage
- Automatically stops when payment is confirmed

### Visual Feedback
- **Checking**: Blue progress indicator with percentage
- **Success**: Green checkmark with success message
- **Timeout**: Orange warning with manual check option
- **Button States**: Disabled during processing

### Backup License Creation
- Manual license creation when payment is confirmed
- Ensures license is created even if webhook fails
- Stores payment cost for accurate license duration
- Handles user authentication automatically

### Error Handling
- Network error resilience
- Timeout management (5 minutes)
- User-friendly error messages
- Graceful fallback to manual checking

## Usage

### For Users
1. Select license duration (1, 3, 6, or 12 months)
2. Click "Сунгах" (Extend) button
3. Payment dialog opens with QR code and bank options
4. Complete payment using any bank app
5. System automatically detects payment and extends license
6. Success message appears and dialog closes

### For Developers
The system is fully integrated and requires no additional setup. Key files:
- `lib/service/payment_service.dart` - Core payment verification logic
- `lib/widgets/enhanced_payment_dialog.dart` - Enhanced payment UI
- `lib/license/license_widget.dart` - Updated license page

## API Endpoints Used

- `POST /api/license/extend-license` - Initiate license extension
- `GET /api/hook/payment/check/:invoiceId` - Check payment status
- `POST /api/hook/payment/create-license-manual` - Create license manually

## Configuration

The system uses the existing API_HOST configuration from `lib/constant.dart`:
```dart
const String API_HOST = "http://www.aslaa.mn";
```

## Benefits

- **Seamless UX**: No manual payment checking required
- **Real-time feedback**: Users see progress immediately
- **Reliable**: Multiple fallback mechanisms ensure license creation
- **Professional**: Modern UI with proper loading states
- **Consistent**: Matches web app functionality

## Technical Details

### Payment Verification Flow
1. User initiates license extension
2. Backend creates payment invoice
3. Frontend starts automatic checking
4. Payment status checked every 5 seconds
5. When payment confirmed, license created automatically
6. User data refreshed and success shown

### Stream-based Architecture
- Uses Dart Streams for real-time updates
- Separates payment logic from UI
- Allows multiple listeners for different UI components
- Proper resource cleanup on disposal

### Error Recovery
- Continues checking despite temporary network errors
- Provides manual check option if automatic fails
- Shows clear error messages to users
- Logs errors for debugging

This implementation provides feature parity with the web application while maintaining the native iOS app experience.
